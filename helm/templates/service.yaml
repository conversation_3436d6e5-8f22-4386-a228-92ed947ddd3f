{{- if .Values.service.enable -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ .Chart.Name }}
  labels:
{{ toYaml .Values.labels | indent 4 }}
spec:
  {{- if eq "headless" (.Values.service.type|lower) }}
  clusterIP: None
  {{- else }}
  type: {{ .Values.service.type }}
  {{- end }}
  ports:
    {{- range .Values.ports }}
    - name: {{ .name }}
      port: {{ .port }}
      protocol: {{ .protocol }}
      targetPort: {{ .name }}
    {{- end }}
  selector:
{{ toYaml .Values.labels | indent 4 }}
{{- end -}}

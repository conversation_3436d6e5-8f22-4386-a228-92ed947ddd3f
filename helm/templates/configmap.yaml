apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Chart.Name }}
data:
  config.yaml: |
    server:
      http:
        addr: {{ .Values.metadataMiddleware.configmap.server.http.addr }}
        timeout: {{ .Values.metadataMiddleware.configmap.server.http.timeout }}
      grpc:
        addr: {{ .Values.metadataMiddleware.configmap.server.grpc.addr }}
        timeout: {{ .Values.metadataMiddleware.configmap.server.grpc.timeout }}
    data:
      type: {{ .Values.metadataMiddleware.configmap.data.type }}
      mountPath: {{ .Values.metadataMiddleware.configmap.data.mountPath }}
      glusterfs:
        ssh:
          host: {{ .Values.metadataMiddleware.configmap.data.glusterfs.ssh.host }}
          port: "{{ .Values.metadataMiddleware.configmap.data.glusterfs.ssh.port }}"
          userName: {{ .Values.metadataMiddleware.configmap.data.glusterfs.ssh.userName }}
          pwd: {{ .Values.metadataMiddleware.configmap.data.glusterfs.ssh.pwd }}
      gpfs:
        ssh:
          host: {{ .Values.metadataMiddleware.configmap.data.gpfs.ssh.host }}
          port: "{{ .Values.metadataMiddleware.configmap.data.gpfs.ssh.port }}"
          userName: {{ .Values.metadataMiddleware.configmap.data.gpfs.ssh.userName }}
          pwd: {{ .Values.metadataMiddleware.configmap.data.gpfs.ssh.pwd }}
      loongstore:
        host: {{ .Values.metadataMiddleware.configmap.data.loongstore.host }}
        port: "{{ .Values.metadataMiddleware.configmap.data.loongstore.port }}"
        userName: {{ .Values.metadataMiddleware.configmap.data.loongstore.userName }}
        pwd: {{ .Values.metadataMiddleware.configmap.data.loongstore.pwd }}
        protocal: {{ .Values.metadataMiddleware.configmap.data.loongstore.protocal }}
        globalResourcePool: {{ .Values.metadataMiddleware.configmap.data.loongstore.globalResourcePool }}
        cert: |-
          {{ .Values.metadataMiddleware.configmap.data.loongstore.cert | nindent 10 }}

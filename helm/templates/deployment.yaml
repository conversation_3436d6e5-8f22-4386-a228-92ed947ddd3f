apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Chart.Name }}
  annotations:
{{ toYaml .Values.annotations | indent 4 }}
  labels:
{{ toYaml .Values.labels | indent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
{{ toYaml .Values.labels | indent 6 }}
  template:
    metadata:
      annotations:
{{ toYaml .Values.annotations | indent 8 }}
      labels:
{{ toYaml .Values.labels | indent 8 }}
    spec:
      securityContext:
{{ toYaml .Values.securityContext | indent 8 }}
      nodeSelector:
{{ toYaml $.Values.nodeSelector | indent 8 }}
      tolerations:
{{ toYaml $.Values.tolerations | indent 8 }}
      affinity:
{{ toYaml $.Values.affinity | indent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          {{- with .Values.image }}
          image: {{ .repository }}/{{ .project }}/{{ .arch }}/{{ .name }}:{{ .tag | default "latest" }}
          imagePullPolicy: {{ .pullPolicy }}
          {{- end }}
          command:
{{ toYaml .Values.command | indent 12 }}
          args:
{{ toYaml .Values.args | indent 12 }}
          env:
{{ toYaml .Values.env | indent 12 }}
          readinessProbe:
{{ toYaml .Values.readinessProbe | indent 12 }}
          livenessProbe:
{{ toYaml .Values.livenessProbe | indent 12 }}
          resources:
{{ toYaml .Values.resources | indent 12 }}
          ports:
            {{- range .Values.ports }}
            - name: {{ .name }}
              containerPort: {{ .port }}
              protocol: {{ .protocol }}
            {{- end }}
          volumeMounts:
            {{- range .Values.mounts }}
            {{- if eq "configmap" ( .type|lower) }}
            - name: {{ $.Chart.Name }}
            {{ else }}
            - name: {{ .name }}
            {{- end }}
              mountPath: {{ .mountPath }}
              {{- if .subPath }}
              subPath: {{ .subPath}}
              {{- end }}
              {{- if .readOnly }}
              readOnly: {{ .readOnly}}
              {{- end }}
            {{- end }}
      volumes:
        {{- range .Values.mounts }}
        {{- if eq "pvc" ( .type|lower) }}
        - name: {{ .name }}
          persistentVolumeClaim:
            claimName: {{ .name }}
        {{- else if eq "configmap" ( .type|lower) }}
        - name: {{ $.Chart.Name }}
          configMap:
            name: {{ $.Chart.Name }}
        {{- else if eq "secret" ( .type|lower) }}
        - name: {{ .name }}
          secret:
            secretName: {{ .name }}
        {{- else if eq "hostpath" ( .type|lower) }}
        - name: {{ .name }}
          hostPath:
            path: {{ .hostpath }}
            type: {{ .hostpathtype }}
        {{- else if eq "emptydir" ( .type|lower) }}
        - name: {{ .name }}
          emptyDir: {}
        {{- end }}
        {{- end }}

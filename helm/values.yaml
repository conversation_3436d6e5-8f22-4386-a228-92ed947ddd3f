# Default values for helm.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
labels:
  job_type: s3
  job_name: metadata-middleware
  branch: develop
  k8s.kuboard.cn/layer: svc
annotations:
  prometheus.io/path: /metrics
  prometheus.io/port: "7070"
  prometheus.io/scrape: "true"
replicaCount: 1

image:
  repository: registry.tecorigin.io:5443
  project: plugins
  arch: x86_64
  name: metadata-middleware
  pullPolicy: IfNotPresent
  tag: ""

command:
  - /bin/metadata-middleware

args:
  - -conf
  - /configs/config.yaml

env:

ports:
  - name: http
    port: 8000
    protocol: TCP
  - name: grpc
    port: 9000
    protocol: TCP
  - name: metric
    port: 7070
    protocol: TCP

service:
  enable: true
  # 服务类型：Headless,ClusterIP,NodePort
  type: NodePort

# 挂载卷类型包括：pvc,configmap,sercet,hostpath,emptydir，vct（statefulset的volumeClaimTemplates）
# 挂载卷名称需要和对应类型名称一致
mounts:
  - name: metadata-middleware-config
    mountPath: /configs/config.yaml
    subPath: config.yaml
    type: configmap
  - name: metadata-middleware-data
    mountPath: /tecofs
    hostpath: /tecofs
    hostpathtype: Directory
    subPath:
    type: hostpath

# 仅当挂载卷类型为pvc或vct需要填写
persistentVolume:
  enable: false

readinessProbe:

livenessProbe:
  failureThreshold: 3
  initialDelaySeconds: 20
  periodSeconds: 10
  timeoutSeconds: 5
  exec:
    command:
      - ls
      - /tecofs/dfs-on

resources:

securityContext:

tolerations:

nodeSelector:
  kubernetes.io/action: platform

affinity:

# configmap
metadataMiddleware:
  configmap:
    server:
      http:
        addr: 0.0.0.0:8000
        timeout: 75s
      grpc:
        addr: 0.0.0.0:9000
        timeout: 75s
    data:
      type: loongstore
      mountPath: /tecofs
      glusterfs:
        ssh:
          host: *********
          port: 22
          userName: develop
          pwd: U32W1A4UqPriVsEz
      gpfs:
        ssh:
          host: *********
          port: 22
          userName: develop
          pwd: U32W1A4UqPriVsEz
      loongstore:
        host: **********
        port: 8558
        userName: admin
        pwd: Loongson@123
        protocal: http
        globalResourcePool: admin
        cert: |-
          -----BEGIN PUBLIC KEY-----
          MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAIvta56yduhTMVHSCkEHofIeBCzwtJVU
          HLQ7G8J0+vBLadxg6MWG0fAzMPPfzPzhz857OfVyBr0BClq6/cgkobcCAwEAAQ==
          -----END PUBLIC KEY-----

# MetaData Middleware

存储元数据管理中间件

## 如何引用

```go
package main

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/backoff"
	"google.golang.org/grpc/credentials/insecure"

	"git-plat.tecorigin.net/ai-platform/backend-lib/logz"
	mdPb "git-plat.tecorigin.net/ai-platform/metadata-middleware/api/md-middleware/v1"
)

func mustDial(addr string) *grpc.ClientConn {
	c, err := grpc.Dial(addr,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithConnectParams(
			grpc.ConnectParams{
				MinConnectTimeout: time.Second * 3,
				Backoff:           backoff.DefaultConfig,
			}))
	if err != nil {
		logz.Fatal("Dial grpc failed", zap.Error(err), zap.String("addr", addr))
	}
	return c
}

func main() {
	logz.Init(&logz.ServiceInfo{
		Module:         "",
		ServiceId:      "",
		ServiceName:    "",
		ServiceVersion: "",
	})
	mdConn := mustDial("10.8.20.2:31691")
	mdCli := mdPb.NewMetaDataMiddlewareClient(mdConn)
	response, err := mdCli.ListQuota(context.TODO(),
		&mdPb.ListQuotaRequest{
			Volume: "defaultClient",
			Type:   mdPb.Quota_TYPE_USAGE,
		})
	if err != nil {
		logz.Fatal("List quota failed", zap.Error(err))
		return
	}

	for _, quota := range response.Quotas {
		fmt.Printf("%v\n", quota)
	}
}
```

## Install Kratos

```
go install github.com/go-kratos/kratos/cmd/kratos/v2@latest
```

## Create a service

```
# Create a template project
kratos new server

cd server
# Add a proto template
kratos proto add api/server/server.proto
# Generate the proto code
kratos proto client api/server/server.proto
# Generate the source code of service by proto file
kratos proto server api/server/server.proto -t internal/service

go generate ./...
go build -o ./bin/ ./...
./bin/server -conf ./configs
```

## Generate other auxiliary files by Makefile

```
# Download and update dependencies
make init
# Generate API files (include: pb.go, http, grpc, validate, swagger) by proto file
make api
# Generate all files
make all
```

## Automated Initialization (wire)

```
# install wire
go get github.com/google/wire/cmd/wire

# generate wire
cd cmd/server
wire
```

## Docker

```bash
# build
docker build -t <your-docker-image-name> .

# run
docker run --rm -p 8000:8000 -p 9000:9000 -v </path/to/your/configs>:/data/conf <your-docker-image-name>
```

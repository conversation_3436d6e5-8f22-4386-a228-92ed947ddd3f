package utils

import (
	"fmt"
	"math/rand"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

// times start from 1
type BackoffFunc func(times int) (time.Duration, error)

func BackoffRetry(fn func() error, backoffFunc BackoffFunc) error {

	var lastErr error
	times := 1
	for {
		log.Infof("Attempt %d\n", times)
		if err := fn(); err == nil {
			return nil
		} else {
			// 否则记录错误并等待退避时间
			lastErr = err
			log.Errorf("Error: %v\n", err)
			backoffTime, err := backoffFunc(times)
			if err != nil {
				break
			}
			log.Infof("Retrying after %v\n", backoffTime)
			time.Sleep(backoffTime)
		}
		times++
	}
	// 达到最大重试次数，返回最后一次错误
	log.Errorf("after %d tries, last error: %v", times, lastErr)
	return lastErr
}

// ConstantBackoff returns a function that always returns the same duration.
func ConstantBackoff(interval time.Duration) BackoffFunc {
	return func(times int) (time.Duration, error) {
		return interval, nil
	}
}

// RandomBackoff returns a function that implements random backoff.
func RandomBackoff(min, max time.Duration) BackoffFunc {
	return func(times int) (time.Duration, error) {
		d := min + time.Duration(rand.Int63n(int64(max-min)))
		return d, nil
	}
}

// LinearBackoffFunc implements a linear backoff function.
func LinearBackoffFunc(base time.Duration) BackoffFunc {
	return func(times int) (time.Duration, error) {
		return base * time.Duration(times), nil
	}
}

// ExponentialBackoff 返回一个简单的指数退避函数
func ExponentialBackoff(base time.Duration) BackoffFunc {
	return func(times int) (time.Duration, error) {
		// 每次重试退避时间翻倍
		backoff := base * time.Duration(1<<uint(times-1))
		// backoff := interval * time.Duration(math.Exp2(float64(timesn))
		return backoff, nil
	}
}

// WithMaxRetries returns a function that will retry at most n times.
func WithMaxRetries(f BackoffFunc, max int) BackoffFunc {
	return func(times int) (time.Duration, error) {
		if times >= max {
			return 0, fmt.Errorf("Exceed max retries: %d max %d", times, max)
		}
		return f(times)
	}
}

// WithJitter adds randdom jitter to the given backoff function.
// range from 0 to maxvalue (jitter param * the backoff interval wanted),
// [0, 1] generally.
func WithJitter(f BackoffFunc, jitter float64) BackoffFunc {
	return func(times int) (time.Duration, error) {
		interval, err := f(times)
		if err != nil {
			return 0, err
		}
		jitterInterval := time.Duration(float64(interval) * jitter)
		return interval + time.Duration(rand.Int63n(int64(jitterInterval))), nil
	}
}

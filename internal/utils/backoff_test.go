package utils

import (
	"errors"
	"math/rand"
	"testing"
	"time"
)

// someFunction 是需要重试的示例函数
func someFunction() error {
	// 这里模拟一个可能失败的函数
	// 比如网络请求、数据库操作等
	// 为了演示，我们简单地随机返回错误
	if rand.Intn(10) != 0 {
		return errors.New("some error occurred")
	}
	return nil
}

func TestConstantBackoff(t *testing.T) {
	BackoffRetry(someFunction, ConstantBackoff(time.Second))
}

func TestRandomBackoff(t *testing.T) {
	BackoffRetry(someFunction, RandomBackoff(1*time.Second, 10*time.Second))
}

func TestLinearBackoffFunc(t *testing.T) {
	BackoffRetry(someFunction, LinearBackoffFunc(1*time.Second))
}

func TestExponentialBackoff(t *testing.T) {
	BackoffRetry(someFunction, ExponentialBackoff(1*time.Second))
}

func TestWithMaxRetries(t *testing.T) {
	BackoffRetry(someFunction, WithMaxRetries(ConstantBackoff(1*time.Second), 3))
}

func TestWithJitter(t *testing.T) {
	BackoffRetry(someFunction, WithJitter(ConstantBackoff(4*time.Second), 0.5))
}

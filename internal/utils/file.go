package utils

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"io/fs"
	"io/ioutil"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/mholt/archiver/v3"
	"github.com/pkg/errors"
)

func CreateDir(path string) error {
	if b := isExist(path); !b {
		err := os.MkdirAll(path, os.ModePerm)
		if err != nil {
			log.Errorf("create dir path:%v failed: %v", path, err.Error())
			return err
		}
	}
	return nil
}

func isExist(path string) bool {
	_, err := os.Stat(path)
	if err == nil {
		return true
	}
	// err happends
	if os.IsExist(err) {
		return true
	}
	return false
}

// 软链检查
func isExistL(path string) bool {
	_, err := os.Lstat(path)
	if err != nil {
		if os.IsExist(err) {
			return true
		}
		return false
	}
	return true
}

func ListDir(dirPath string) ([]string, error) {
	var filenames []string
	err := filepath.Walk(dirPath, func(path string, info fs.FileInfo, err error) error {
		if path == dirPath {
			return nil
		} else if strings.HasPrefix(info.Name(), ".") || strings.HasPrefix(info.Name(), "_") {
			if info.IsDir() {
				return filepath.SkipDir
			}
			return nil
		} else if info.IsDir() {
			return nil
		} else {
			filenames = append(filenames, path)
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return filenames, nil
}

func CopyFile(src string, dest string) error {
	srcFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer srcFile.Close()

	destFile, err := os.Create(dest)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, srcFile)
	if err != nil {
		return err
	}

	return nil
}
func ExtractFile(uploadedFile string) error {
	ext := getExtract(uploadedFile)
	switch strings.ToLower(ext) {
	case ".zip", ".tar.gz", ".tar", ".rar", ".gz":
		err := extractPackage(uploadedFile, filepath.Dir(uploadedFile))
		if err != nil {
			return err
		}
		return nil
	default:
		return nil
	}
}

var exts = []string{".tar.gz", ".tgz", ".gz", ".zip", ".rar"}

func getExtract(filename string) string {
	found := ""
	for _, ext := range exts {
		if strings.HasSuffix(filename, ext) {
			if found == "" || len(found) < len(ext) {
				found = ext
			}
		}
	}
	return found
}

func extractPackage(uploadedFile, destDir string) error {
	err := archiver.Unarchive(uploadedFile, destDir)
	if err != nil {
		log.Errorf("failed to extract RAR file %s: %v", uploadedFile, err)
		return err
	}
	log.Infof("Unarchive.")
	return nil
}

func ContainsExt(s []string, ext string) bool {
	for _, a := range s {
		if a == ext {
			return true
		}
	}
	return false
}

func ListDirByReadFir(path string) ([]string, error) {
	var files []string
	fileInfo, err := ioutil.ReadDir(path)
	if err != nil {
		return files, err
	}
	for _, file := range fileInfo {
		files = append(files, file.Name())
	}
	return files, nil
}

// DiskUsage 使用du命令计算指定路径下的磁盘总用量，并增加超时控制
func DiskUsage(path string, timeout time.Duration) (int64, error) {
	// 创建一个带有超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	// 使用带有上下文的命令执行
	cmd := exec.CommandContext(ctx, "du", "-s", "-b", path)
	var out, errOut bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &errOut
	err := cmd.Run()
	if err != nil {
		fmt.Println(err, errOut.String(), out.String())
		return 0, errors.Wrapf(
			err, "errStr=%s, outStr=%s", errOut.String(), out.String())
	}

	// 解析输出
	output := strings.Fields(out.String())
	if len(output) < 1 {
		return 0, fmt.Errorf("unexpected output from du command")
	}

	// 字节数从字符串转换为int64
	size, err := strconv.ParseInt(output[0], 10, 64)
	if err != nil {
		return 0, err
	}

	return size, nil
}

func ObjectsCount(path string, timeout time.Duration) (int64, error) {
	// 创建一个带有超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	// 使用带有上下文的命令执行
	var out bytes.Buffer
	cmd1 := exec.CommandContext(ctx, "find", path, "-type", "f")
	cmd2 := exec.CommandContext(context.TODO(), "wc", "-l")
	reader, _ := cmd1.StdoutPipe()
	cmd2.Stdin = reader
	cmd2.Stdout = &out
	defer reader.Close()

	if err := cmd1.Start(); err != nil {
		log.Errorf("run command find failed: %v", err)
		return 0, err
	}
	if err := cmd2.Start(); err != nil {
		log.Errorf("run command wc failed: %v", err)
		return 0, err
	}
	if err := cmd1.Wait(); err != nil {
		log.Errorf("wait command find failed: %v", err)
		return 0, err
	}
	if err := cmd2.Wait(); err != nil {
		log.Errorf("wait command wc failed: %v", err)
		return 0, err
	}

	// 解析输出
	output := strings.Fields(out.String())
	if len(output) < 1 {
		return 0, fmt.Errorf("unexpected output from du command")
	}

	// 字节数从字符串转换为int64
	size, err := strconv.ParseInt(output[0], 10, 64)
	if err != nil {
		return 0, err
	}

	return size, nil
}

package utils

import (
	"bytes"
	"errors"

	"golang.org/x/crypto/ssh"
)

type Session struct {
	*ssh.Session
}

func (s *Session) AllOutput(cmd string) ([]byte, []byte, error) {
	if s.Stdout != nil {
		return nil, nil, errors.New("ssh: Stdout already set")
	}
	if s.Stderr != nil {
		return nil, nil, errors.New("ssh: Stderr already set")
	}
	var outB bytes.Buffer
	var errB bytes.Buffer
	s.Stdout = &outB
	s.Stderr = &errB
	err := s.Run(cmd)
	return outB.Bytes(), errB.Bytes(), err
}

package utils

import (
	"context"
	"fmt"
	"net"
	"time"

	"github.com/pkg/errors"
	"golang.org/x/crypto/ssh"
)

type SSHConnection struct {
	sshConfig *ssh.ClientConfig
	client    *ssh.Client

	host     string
	port     uint16
	user     string
	password string
}

func NewSSHConnection(host string, port uint16, user, password string) (
	*SSHConnection, error) {

	sshConfig := &ssh.ClientConfig{
		User: user,
		Auth: []ssh.AuthMethod{
			ssh.Password(password),
		},
		HostKeyCallback: ssh.HostKeyCallback(
			func(string, net.Addr, ssh.PublicKey) error {
				return nil
			},
		),
		Timeout: 10 * time.Second,
	}

	hostPort := fmt.Sprintf("%s:%d", host, port)
	client, err := ssh.Dial("tcp", hostPort, sshConfig)
	if err != nil {
		return nil, err
	}

	return &SSHConnection{
		sshConfig: sshConfig,
		client:    client,
		host:      host,
		port:      port,
		user:      user,
		password:  password,
	}, nil
}

func (conn *SSHConnection) Close() {
	if conn.client != nil {
		conn.client.Close()
		conn.client = nil
	}
}

func (conn *SSHConnection) SendCommands(command string) (string, string, error) {

	clientSession, err := conn.client.NewSession()
	if err != nil {
		return "", "", err
	}
	session := Session{clientSession}

	ctx, cancel := context.WithTimeout(context.TODO(), SSH_COMMAND_TIMEOUT)
	defer cancel()

	outChan := make(chan interface{}, 3)

	go func() {
		sendCmd := fmt.Sprintf("echo '%s' | sudo -S %s", conn.password, command)
		outBytes, errBytes, er := session.AllOutput(sendCmd)
		outChan <- string(outBytes)
		outChan <- string(errBytes)
		outChan <- er
	}()

	var output string
	var errOutput string
	select {
	case content := <-outChan:
		output = content.(string)
		errOutput = (<-outChan).(string)
		if errT := <-outChan; errT != nil {
			err = errT.(error)
		} else {
			err = nil
		}
	case <-ctx.Done():
		err = fmt.Errorf("ssh run command timed out: %s", command)
	}

	if err != nil {
		if errOutput == "" {
			return output, errOutput, err
		}
		return output, errOutput, errors.Wrapf(err, errOutput)
	}

	return output, errOutput, nil
}

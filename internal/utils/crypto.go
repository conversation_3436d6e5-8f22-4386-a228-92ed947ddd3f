package utils

import (
	"crypto/md5"
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/hex"
	"encoding/pem"
	"fmt"
	"strings"
)

func CalculateMD5(input string) string {
	// 创建一个MD5哈希对象
	hasher := md5.New()

	// 将字符串转换为字节数组并写入哈希对象
	hasher.Write([]byte(input))

	// 计算哈希值并转换为十六进制格式
	hashInBytes := hasher.Sum(nil)
	hashAsString := hex.EncodeToString(hashInBytes)

	return strings.ToLower(hashAsString)
}

// RSA加密, 使用公钥加密， 返回base64编码后的字符串
func EncryptWithCert(pubkeyPem string, message []byte) (string, error) {

	// 1. 解码PEM格式的公钥
	block, _ := pem.Decode([]byte(pubkeyPem))
	if block == nil {
		return "", fmt.Errorf("failed to parse PEM block containing the public key")
	}

	// 2. 解析公钥
	pub, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return "", fmt.Errorf("failed to parse DER encoded public key: %v", err)
	}
	publicKey := pub.(*rsa.PublicKey)

	// 3. 使用公钥加密
	ciphertext, err := rsa.EncryptPKCS1v15(rand.Reader, publicKey, message)
	if err != nil {
		return "", fmt.Errorf("failed to encrypt: %v", err)
	}

	// 4. 将加密后的数据转换为base64
	base64Ciphertext := base64.StdEncoding.EncodeToString(ciphertext)
	return base64Ciphertext, nil
}

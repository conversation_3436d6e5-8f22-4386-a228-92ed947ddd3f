package model

import pb "git-plat.tecorigin.net/ai-platform/metadata-middleware/api/md-middleware/v1"

type Quota struct {
	Volume string        `json:"volume,omitempty"`
	Type   pb.Quota_Type `json:"type,omitempty"`
	Path   string        `json:"path,omitempty"`

	// -1 means no limit, 0 means limit to zero,
	// positive number means absolute size in bytes,
	// otherwise, it is the illegal state.
	Size    int64 `json:"size,omitempty"`
	Percent int32 `json:"percent,omitempty"`
	Used    int64 `json:"used,omitempty"`
}

func NewQuotaFromSetRequest(pbReq *pb.SetQuotaRequest) *Quota {
	return &Quota{
		Volume:  pbReq.Volume,
		Type:    pbReq.Type,
		Path:    pbReq.Path,
		Size:    pbReq.Size,
		Percent: pbReq.Percent,
	}
}

func NewQuotaFromUnsetRequest(pbReq *pb.UnsetQuotaRequest) *Quota {
	return &Quota{
		Volume: pbReq.Volume,
		Type:   pbReq.Type,
		Path:   pbReq.Path,
	}
}

func NewQuotaFromRemoveRequest(pbReq *pb.RemoveQuotaRequest) *Quota {
	return &Quota{
		Volume: pbReq.Volume,
		Path:   pbReq.Path,
	}
}

func (q *Quota) PBQuota() *pb.Quota {
	return &pb.Quota{
		Volume:  q.Volume,
		Type:    q.Type,
		Path:    q.Path,
		Size:    q.Size,
		Percent: q.Percent,
		Used:    q.Used,
	}
}

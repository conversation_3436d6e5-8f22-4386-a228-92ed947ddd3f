package model

import (
	"fmt"
	"strings"
)

type DFS_TYPE uint8

const (
	Unknown    DFS_TYPE = iota
	Glusterfs           // 1
	GPFS                // 2
	LoongStore          // 3
	SangforEDS          // 4
)

func String2DFS_TYPE(value string) DFS_TYPE {
	iValue := strings.ToLower(value)
	switch iValue {
	case "glusterfs":
		return Glusterfs
	case "gpfs":
		return GPFS
	case "loongstore":
		return LoongStore
	case "sangforeds":
		return SangforEDS
	}
	return Unknown
}

func (typeV DFS_TYPE) String() string {
	switch typeV {
	case Unknown:
		return "Unknown"
	case Glusterfs:
		return "Glusterfs"
	case GPFS:
		return "GPFS"
	case LoongStore:
		return "LoongStore"
	case SangforEDS:
		return "SangforEDS"
	}
	return fmt.Sprintf("DFS_TYPE(%d)", typeV)
}

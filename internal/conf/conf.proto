syntax = "proto3";
package kratos.api;

option go_package = "metadata-middleware/internal/conf;conf";

import "google/protobuf/duration.proto";

message Bootstrap {
  Server server = 1;
  Data data = 2;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  HTTP http = 1;
  GRPC grpc = 2;
}

message SSH {
  string host = 1;
  int32 port = 2;
  string userName = 3;
  string pwd = 4; 
}

message Glusterfs {
  SSH ssh = 1;
}

message GPFS {
  SSH ssh = 1;
}

message LoongStore{
  string host = 1;
  int32 port = 2;
  string userName = 3;
  string pwd = 4; 
  string protocal = 5;
  string globalResourcePool = 6;
  string cert = 7;
}

message Data {
  string type = 1;
  string mountPath = 2;
  Glusterfs glusterfs = 3;
  GPFS gpfs = 4;
  LoongStore loongstore = 5;
}

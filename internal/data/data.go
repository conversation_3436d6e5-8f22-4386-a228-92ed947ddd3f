package data

import (
	"fmt"

	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/conf"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/data/dfs"
	repo "git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/data/dfs/repository"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/model"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
)

// ProviderSet is data providers.
var ProviderSet = wire.NewSet(NewDfsRepo)

// NewDfsRepo .
func NewDfsRepo(c *conf.Data, logger log.Logger) (dfs.IDfsRepo, func(), error) {
	var quotaRepo dfs.IDfsRepo
	var cleanFunc dfs.CleanupFunc
	var err error

	fsType := model.String2DFS_TYPE(c.Type)
	switch fsType {
	case model.Glusterfs:
		quotaRepo, cleanFunc, err = repo.NewGlusterRepo(
			c.<PERSON>, c.<PERSON>, logger)
	case model.GPFS:
		quotaRepo, cleanFunc, err = repo.NewGPFSRepo(c)
	case model.LoongStore:
		quotaRepo, cleanFunc, err = repo.NewLoongStoreRepo(
			c.MountPath, c.Loongstore, logger)
	case model.SangforEDS:
		fallthrough
	default:
		return nil, nil, fmt.Errorf("Unknown dfs type: %s", fsType.String())
	}

	return quotaRepo, cleanFunc, err
}

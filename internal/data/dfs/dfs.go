package dfs

import (
	"context"

	pb "git-plat.tecorigin.net/ai-platform/metadata-middleware/api/md-middleware/v1"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/model"
)

type IDfsRepo interface {
	// create dir and set the quota, idempotence
	CreateQuotaDir(context.Context, *model.Quota) error
	// set the quota only, dir exists already, idempotence
	SetQuota(context.Context, *model.Quota) error
	// list quotas for the given paths, batch operation
	// list all quotas when the path list param is nil,
	// return only the usage if the path has no quota set, and the return size = -1
	ListQuota(ctx context.Context, volume string, quotaType pb.Quota_Type,
		paths []string) ([]*model.Quota, error)
	// remove the quota and the directory.
	RemoveQuotaDir(context.Context, *model.Quota) error
	// unset the quota only, directory remains.
	UnsetQuota(context.Context, *model.Quota) error
	// Mv src to dst
	Mv(ctx context.Context, volume, src, dst string) error
}

type CleanupFunc func()

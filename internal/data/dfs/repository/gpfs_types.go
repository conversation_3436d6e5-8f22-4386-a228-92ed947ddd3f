package repository

const (
	GPFSBinPath              = "/usr/lpp/mmfs/bin/"
	GPFSCreateFilesetCommand = GPFSBinPath + "mmcrfileset %s %s"                         // filesystem filesetName
	GPFSDeleteFilesetCommand = GPFSBinPath + "mmdelfileset %s %s -f"                     // filesystem filesetName
	GPFSCheckFilesetCommand  = GPFSBinPath + "mmlsfileset %s %s | awk 'NR>2 {print $2}'" // filesystem filesetName
	GPFSListFilesetCommand   = GPFSBinPath + "mmlsfileset %s | awk 'NR>2'"               // filesystem
	GPFSLinkFilesetCommand   = GPFSBinPath + "mmlinkfileset %s %s -J %s"                 // filesystem filesetName mountPath
	GPFSUnLinkFilesetCommand = GPFSBinPath + "mmunlinkfileset %s %s -f"                  // filesystem filesetName
	GPFSSetBlockQuotaCommand = GPFSBinPath + "mmsetquota %s:%s --block %d:%d"            // filesystem filesetName quotaSize quotaUnit
	GPFSSetFileQuotaCommand  = GPFSBinPath + "mmsetquota %s:%s --files %d:%d"
	GPFSRepQuotaCommand      = GPFSBinPath + "mmrepquota -j %s | awk 'NR>1'" // filesystem
)

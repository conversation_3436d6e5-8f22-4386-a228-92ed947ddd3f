package repository

import (
	"bytes"
	"context"
	"fmt"
	"os/exec"
	"path"
	"strconv"
	"strings"

	"git-plat.tecorigin.net/ai-platform/backend-lib/logz"
	errV1 "git-plat.tecorigin.net/ai-platform/errno/api/err/v1"
	"go.uber.org/zap"

	pb "git-plat.tecorigin.net/ai-platform/metadata-middleware/api/md-middleware/v1"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/conf"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/data/dfs"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/model"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/utils"
)

var _ dfs.IDfsRepo = new(GPFSRepo)

type GPFSRepo struct {
	cfg       *conf.GPFS
	mountPath string
	sshConn   *utils.SSHConnection
	l         *logz.Logger
}

type gpfsUsage struct {
	Fileset   string
	BlockUsed int64
	BlockSoft int64
	BlockHard int64
	FileUsed  int64
	FileSoft  int64
	FileHard  int64
}

func NewGPFSRepo(c *conf.Data) (*GPFSRepo, func(), error) {
	cfg := c.Gpfs
	l := logz.Default().With(zap.String("dfs", "gpfs-ssh"))
	sshConn, err := utils.NewSSHConnection(
		cfg.Ssh.Host, uint16(cfg.Ssh.Port), cfg.Ssh.UserName, cfg.Ssh.Pwd)
	if err != nil {
		logz.Err("new ssh conn failed", err)
		return nil, nil, err
	}
	cleanFunc := func() {
		sshConn.Close()
	}
	return &GPFSRepo{
		cfg:       cfg,
		mountPath: c.MountPath,
		sshConn:   sshConn,
		l:         l,
	}, cleanFunc, nil
}

func (g *GPFSRepo) CreateQuotaDir(ctx context.Context, quota *model.Quota) error {
	fileset := g.filesetName(quota.Path)
	exi, linked, err := g.filesetStat(quota.Volume, fileset)
	if err != nil {
		return err
	}
	if exi && linked {
		return g.SetQuota(ctx, quota)
	}

	var (
		output, errStr string
		fullPath       = path.Join(g.mountPath, quota.Path)
	)
	if !exi {
		crFilesetCommand := fmt.Sprintf("echo '%s' | sudo -S %s",
			g.cfg.Ssh.Pwd,
			fmt.Sprintf(GPFSCreateFilesetCommand, quota.Volume, fileset))
		if output, errStr, err = g.sshConn.SendCommands(crFilesetCommand); err != nil {
			g.l.Err("create fileset failed", err, zap.String("command", crFilesetCommand), zap.String("errStr", errStr))
			return err
		}
		g.l.Info("create fileset success", zap.String("output", output), zap.String("errStr", errStr), zap.String("command", crFilesetCommand))
	}
	if !linked {
		linkFilesetCommand := fmt.Sprintf("echo '%s' | sudo -S %s",
			g.cfg.Ssh.Pwd,
			fmt.Sprintf(GPFSLinkFilesetCommand, quota.Volume, fileset, fullPath))
		if output, errStr, err = g.sshConn.SendCommands(linkFilesetCommand); err != nil {
			g.l.Err("link fileset failed", err, zap.String("command", linkFilesetCommand), zap.String("errStr", errStr))
			return err
		}
		g.l.Info("link fileset success", zap.String("output", output), zap.String("errStr", errStr), zap.String("command", linkFilesetCommand))
	}
	return g.SetQuota(ctx, quota)
}

func (g *GPFSRepo) SetQuota(ctx context.Context, quota *model.Quota) error {

	// resolving special values.
	// set to no limit
	if quota.Size == -1 {
		quota.Size = 0
	}
	// limit quota to zero
	if quota.Size == 0 {
		quota.Size = 1
	}

	var (
		err             error
		output, errStr  string
		fileset         = g.filesetName(quota.Path)
		setQuotaCommand string
	)
	switch quota.Type {
	case pb.Quota_TYPE_USAGE:
		setQuotaCommand = GPFSSetBlockQuotaCommand
	case pb.Quota_TYPE_OBJECTS:
		setQuotaCommand = GPFSSetFileQuotaCommand
	default:
		return errV1.ErrorQuotaInvalidType(quota.Type.String())
	}
	command := fmt.Sprintf("echo '%s' | sudo -S %s",
		g.cfg.Ssh.Pwd,
		fmt.Sprintf(setQuotaCommand, quota.Volume, fileset, quota.Size,
			quota.Size))
	if output, errStr, err = g.sshConn.SendCommands(command); err != nil {
		g.l.Err("set quota fail", err, zap.String("command", command))
		return err
	}

	g.l.Info("set quota success", zap.String("output", output), zap.String("errStr", errStr), zap.String("command", command))
	return nil
}

func (g *GPFSRepo) ListQuota(
	ctx context.Context, fileSystem string, quotaType pb.Quota_Type,
	paths []string) ([]*model.Quota, error) {

	switch quotaType {
	case pb.Quota_TYPE_USAGE:
	default:
		return nil, errV1.ErrorQuotaInvalidType(quotaType.String())
	}

	var (
		err          error
		ret          []*model.Quota
		filesetM     map[string]string     // mountPath->filesetName
		filesetUsage map[string]*gpfsUsage // filesetName->usage
	)
	if filesetM, err = g.filesetM(fileSystem); err != nil {
		return nil, err
	}
	if filesetUsage, err = g.listFilesetUsage(fileSystem); err != nil {
		return nil, err
	}
	if len(paths) == 0 {
		for k, v := range filesetM {
			if usage, exi := filesetUsage[v]; exi {
				size := usage.BlockSoft
				if size == 0 {
					size = -1
				}
				ret = append(ret, &model.Quota{
					Volume:  fileSystem,
					Type:    quotaType,
					Path:    k,
					Used:    usage.BlockUsed,
					Size:    size,
					Percent: 100,
				})
			}
		}
		return ret, nil
	}

	for _, v := range paths {
		linkPath := strings.TrimSuffix(path.Join(g.mountPath, v), "/")
		if fileset, ok := filesetM[linkPath]; ok {
			if usage, exi := filesetUsage[fileset]; exi {
				size := usage.BlockSoft
				if size == 0 {
					size = -1
				}
				ret = append(ret, &model.Quota{
					Volume:  fileSystem,
					Type:    quotaType,
					Path:    v,
					Used:    usage.BlockUsed,
					Size:    size,
					Percent: 100,
				})
			}
			continue
		}

		usage, er := utils.DiskUsage(path.Join(g.mountPath, v), utils.DISK_USAGE_TIMEOUT)
		if er != nil {
			g.l.Err("check disk usage failed", er, zap.String("path", v))
		}
		ret = append(ret, &model.Quota{
			Volume:  fileSystem,
			Type:    quotaType,
			Path:    v,
			Used:    usage,
			Size:    -1,
			Percent: 100,
		})
	}
	return ret, err
}

func (g *GPFSRepo) RemoveQuotaDir(ctx context.Context, quota *model.Quota) error {
	var (
		err            error
		output, errStr string
		fileset        = g.filesetName(quota.Path)
		command        = fmt.Sprintf("echo '%s' | sudo -S %s", g.cfg.Ssh.Pwd, fmt.Sprintf(GPFSUnLinkFilesetCommand, quota.Volume, fileset))
	)
	if output, errStr, err = g.sshConn.SendCommands(command); err != nil {
		g.l.Err("unlink fileset fail", err, zap.String("command", command), zap.String("errStr", errStr))
		return err
	}
	g.l.Info("unlink fileset success", zap.String("output", output), zap.String("errStr", errStr), zap.String("command", command))
	command = fmt.Sprintf("echo '%s' | sudo -S %s", g.cfg.Ssh.Pwd, fmt.Sprintf(GPFSDeleteFilesetCommand, quota.Volume, fileset))
	if output, errStr, err = g.sshConn.SendCommands(command); err != nil {
		g.l.Err("delete fileset fail", err, zap.String("command", command), zap.String("errStr", errStr))
		return err
	}
	g.l.Info("delete fileset success", zap.String("output", output), zap.String("errStr", errStr), zap.String("command", command))
	return nil
}

func (g *GPFSRepo) UnsetQuota(ctx context.Context, quota *model.Quota) error {
	quota.Size = 0
	return g.SetQuota(ctx, quota)
}

func (r *GPFSRepo) Mv(ctx context.Context, volume, src, dst string) error {
	srcMntPath := fmt.Sprintf("%s%s", r.mountPath, src)
	dstMntPath := fmt.Sprintf("%s%s", r.mountPath, dst)
	cmd := exec.CommandContext(ctx, "mv", srcMntPath, dstMntPath)
	var out, errOut bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &errOut
	err := cmd.Run()
	if err != nil {
		return errV1.ErrorFileOperateError(
			"mv %s to %s failed: %v, errStr=%s, outStr=%s",
			srcMntPath, dstMntPath, err, errOut.String(), out.String())
	}
	return nil
}

func (g *GPFSRepo) listFilesetUsage(fileSystem string) (map[string]*gpfsUsage, error) {
	var (
		err            error
		output, errStr string
		command        = fmt.Sprintf("echo '%s' | sudo -S %s",
			g.cfg.Ssh.Pwd, fmt.Sprintf(GPFSRepQuotaCommand, fileSystem))
	)
	if output, errStr, err = g.sshConn.SendCommands(command); err != nil {
		g.l.Err("list fileset usage fail", err,
			zap.String("command", command), zap.String("errStr", errStr))
		return nil, errV1.ErrorQuotaOperateFailed("list fileset usage fail: %v", err)
	}

	m := make(map[string]*gpfsUsage)
	k2b := func(val int64) int64 {
		return val * 1024
	}
	parseSize := func(s string) int64 {
		val, e := strconv.ParseInt(s, 10, 64)
		if e != nil {
			return 0
		}
		return val
	}
	parseBlockSize := func(s string) int64 {
		return k2b(parseSize(s))
	}

	lines := strings.Split(output, "\n")
	if len(lines) < 2 { // 需要至少包含表头和一行数据
		return m, nil
	}

	// 解析表头获取列索引
	headers := strings.Fields(lines[0])

	// 查找关键列的索引
	var (
		nameIndex       = -1
		kbIndex         = -1
		blockQuotaIndex = -1 // 第一个quota列
		blockLimitIndex = -1 // 第一个limit列
		filesIndex      = -1
		fileQuotaIndex  = -1 // 第二个quota列
		fileLimitIndex  = -1 // 第二个limit列
	)

	// 遍历表头找到所有需要的列索引
	for i, header := range headers {
		switch header {
		case "Name":
			nameIndex = i
		case "KB":
			kbIndex = i
		case "quota":
			if blockQuotaIndex == -1 {
				blockQuotaIndex = i
			} else {
				fileQuotaIndex = i
			}
		case "limit":
			if blockLimitIndex == -1 {
				blockLimitIndex = i
			} else {
				fileLimitIndex = i
			}
		case "files":
			filesIndex = i
		}
	}

	// 验证所有必需的列是否都找到了
	if nameIndex == -1 || kbIndex == -1 || blockQuotaIndex == -1 ||
		blockLimitIndex == -1 || filesIndex == -1 || fileQuotaIndex == -1 ||
		fileLimitIndex == -1 {
		return nil, errV1.ErrorQuotaParseFailed("missing required columns in output")
	}

	// 解析数据行
	for _, line := range lines[1:] {
		fields := strings.Fields(line)
		if len(fields) != len(headers) {
			g.l.Warn("line is not valid", zap.String("line", line))
			continue
		}

		name := fields[nameIndex]
		m[name] = &gpfsUsage{
			Fileset:   name,
			BlockUsed: parseBlockSize(fields[kbIndex]),
			BlockSoft: parseBlockSize(fields[blockQuotaIndex]),
			BlockHard: parseBlockSize(fields[blockLimitIndex]),
			FileUsed:  parseSize(fields[filesIndex]),
			FileSoft:  parseSize(fields[fileQuotaIndex]),
			FileHard:  parseSize(fields[fileLimitIndex]),
		}
	}

	return m, nil
}

// filesetM mountPath->filesetName
func (g *GPFSRepo) filesetM(fileSystem string) (map[string]string, error) {
	var (
		err            error
		output, errStr string
		command        = fmt.Sprintf("echo '%s' | sudo -S %s",
			g.cfg.Ssh.Pwd, fmt.Sprintf(GPFSListFilesetCommand, fileSystem))
	)
	if output, errStr, err = g.sshConn.SendCommands(command); err != nil {
		g.l.Err("list fileset fail", err, zap.String("command", command), zap.String("errStr", errStr))
		return nil, err
	}
	m := make(map[string]string)
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		if !strings.Contains(line, g.mountPath) {
			continue
		}
		fields := strings.Fields(line)
		m[fields[2]] = fields[0]
	}
	return m, nil
}

// filesetExists check if fileset exists and linked
func (g *GPFSRepo) filesetStat(filesystem, fileset string) (bool, bool, error) {
	var (
		err            error
		output, errStr string
		command        = fmt.Sprintf("echo '%s' | sudo -S %s", g.cfg.Ssh.Pwd, fmt.Sprintf(GPFSCheckFilesetCommand, filesystem, fileset))
	)
	if output, errStr, err = g.sshConn.SendCommands(command); err != nil {
		g.l.Err("check if fileset exists fail", err, zap.String("command", command), zap.String("errStr", errStr))
		return false, false, err
	}
	exi := strings.Contains(output, "Linked") || strings.Contains(output, "Unlinked")
	linked := strings.Contains(output, "Linked")
	g.l.Info("fileset stat", zap.String("fileset", fileset), zap.String("output", output), zap.Bool("exi", exi), zap.Bool("linked", linked))
	return exi, linked, nil
}

func (g *GPFSRepo) filesetName(path string) string {
	path = strings.Trim(path, "/")
	ret := strings.ReplaceAll(path, "/", ".")
	ret = strings.ReplaceAll(ret, "tenant-", "")
	ret = strings.ReplaceAll(ret, "workspace-", "")
	return strings.ReplaceAll(ret, "project-", "")
}

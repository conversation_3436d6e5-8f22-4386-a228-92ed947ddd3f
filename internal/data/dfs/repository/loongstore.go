package repository

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"sync"
	"time"

	sjson "github.com/bitly/go-simplejson"
	kErrors "github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"

	errV1 "git-plat.tecorigin.net/ai-platform/errno/api/err/v1"
	pb "git-plat.tecorigin.net/ai-platform/metadata-middleware/api/md-middleware/v1"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/conf"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/data/dfs"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/model"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/utils"
)

type loongstoreRepo struct {
	mntPath string
	c       *conf.LoongStore

	client      *http.Client
	accessToken string

	lock sync.Mutex // gluster operation has global lock.
	log  *log.Helper
}

func NewLoongStoreRepo(mntPath string, c *conf.LoongStore, logger log.Logger) (
	dfs.IDfsRepo, dfs.CleanupFunc, error) {

	client := http.Client{
		Transport: &http.Transport{ //对客户端进行一些配置
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
		Timeout: utils.SSH_COMMAND_TIMEOUT,
	}

	token, err := getAccessToken(&client, c)
	if err != nil {
		rErr := errors.Wrap(
			err, "getAccessToken failed when init loongstore repo.")
		return nil, nil, rErr
	}
	logger.Log(log.LevelInfo, "accessToken", token)

	cleanFunc := func() { client.CloseIdleConnections() }
	return &loongstoreRepo{
		mntPath:     mntPath,
		c:           c,
		client:      &client,
		accessToken: token,
		log:         log.NewHelper(logger),
	}, cleanFunc, nil
}

func (r *loongstoreRepo) CreateQuotaDir(
	ctx context.Context, quota *model.Quota) error {

	//1. mkdir
	if err := r.mkdir(ctx, quota); err != nil {
		return err
	}

	//2. add quota or update quota
	if err := r.SetQuota(ctx, quota); err != nil {
		return err
	}

	return nil
}

func (r *loongstoreRepo) SetQuota(
	ctx context.Context, quota *model.Quota) error {
	return r.setQuotaInternal(ctx, quota, false)
}

func (r *loongstoreRepo) setQuotaInternal(
	ctx context.Context, quota *model.Quota, delay bool) error {
	// check volume
	_, err := r.listDir(quota.Volume, quota.Path)
	if err != nil {
		return errV1.ErrorFileNotFound(err.Error())
	}

	lQuota, err := NewLoongStoreQuotaFromModel(quota)
	if err != nil {
		return err
	}

	// 0. check quota existance
	originQuota, err := r.querySingleQuota(ctx, lQuota)
	if err != nil {
		return err
	}

	// 1. add quota or update quota
	if originQuota != nil {
		// 1.0 set quota, set the alarm percent.
		// update quota
		if err = originQuota.Merge(lQuota, quota.Type); err != nil {
			return err
		}
		if err = r.updateQuota(ctx, originQuota); err != nil {
			return err
		}
		// update alarm threshold
		if err = r.setQuotaThreshold(ctx, originQuota); err != nil {
			return err
		}
		return nil
	}

	// 1.1 add quota, set the alarm percent
	// add quota
	err = r.createQuota(ctx, lQuota)
	if err != nil {
		kErr, ok := err.(*kErrors.Error)
		if !ok || !strings.Contains(kErr.Message, QUOTA_EXIST) {
			return err
		} else {
			// add threshold asyncronously by retry
			time.AfterFunc(ASYNC_QUOTA_DELAY_SECONDS*time.Second, func() {
				err := r.setQuotaInternal(context.TODO(), quota, true)
				if err != nil {
					r.log.Errorf("Async setQuota for %s failed: %v", quota.Path, err)
				} else {
					r.log.Infof("Async setQuota for %s success.", quota.Path)
				}
			})
			return nil
		}
	}

	if !delay {
		// add threshold asyncronously by retry
		time.AfterFunc(ASYNC_QUOTA_DELAY_SECONDS*time.Second, func() {
			err := r.setQuotaInternal(context.TODO(), quota, true)
			if err != nil {
				r.log.Errorf("Async setQuota for %s failed: %v", quota.Path, err)
			} else {
				r.log.Infof("Async setQuota for %s success.", quota.Path)
			}
		})
		return nil
	}

	// delay request, but the quota is gone, maybe deleted by user.
	// so we don't need to add threshold.
	r.log.Warnf("Delay setQuota request, but the quota is gone, "+
		"maybe deleted by user, Just Unset it asynchronously here, "+
		"you could check and delete it manually! quota=%v", quota)
	time.AfterFunc(ASYNC_QUOTA_DELAY_SECONDS*time.Second, func() {
		err := r.UnsetQuota(context.TODO(), quota)
		if err != nil {
			r.log.Errorf("Async UnsetQuota for %s failed: %v", quota.Path, err)
		} else {
			r.log.Infof("Async UnsetQuota for %s success.", quota.Path)
		}
	})
	return nil
}

func (r *loongstoreRepo) ListQuota(
	ctx context.Context, volume string, quotaType pb.Quota_Type, paths []string) (
	[]*model.Quota, error) {

	// check volume
	_, err := r.listDir(volume, "")
	if err != nil {
		return nil, err
	}

	// one path
	if len(paths) == 1 {
		originQuota, err := r.querySingleQuota(
			ctx, &LoongStoreQuota{Path: fmt.Sprintf("/%s%s", volume, paths[0])})
		if err != nil {
			return nil, err
		}
		if originQuota != nil {
			quota, err := originQuota.QuotaModle(volume, quotaType)
			if err != nil {
				return nil, err
			}
			return []*model.Quota{quota}, nil
		}

		// no qutoa, 统计返回
		rQuota, err := r.statSingleDir(volume, quotaType, paths[0])
		if err != nil {
			return nil, err
		}
		return []*model.Quota{rQuota}, nil
	}

	// zero path
	lQuotas, err := r.queryAllQuotas(volume)
	if err != nil {
		return nil, err
	}

	quotas := make([]*model.Quota, 0)
	for _, lQuota := range lQuotas {
		quota, err := lQuota.QuotaModle(volume, quotaType)
		if err != nil {
			r.log.Errorf("Convert loongstore quota failed: %v", lQuota)
			continue
		}
		quotas = append(quotas, quota)
	}

	if len(paths) == 0 {
		if len(lQuotas) == 0 {
			return nil, nil
		}
		if len(quotas) == 0 {
			return nil, errV1.ErrorQuotaOperateFailed(
				"Convert loongstore to quota failed.")
		}
		return quotas, nil
	}

	// multi path
	quotaMap := make(map[string]*model.Quota)
	for _, quota := range quotas {
		quotaMap[quota.Path] = quota
	}
	rQuotas := make([]*model.Quota, 0)
	for _, path := range paths {
		if quota, ok := quotaMap[path]; ok {
			rQuotas = append(rQuotas, quota)
			continue
		}
		quota, err := r.statSingleDir(volume, quotaType, path)
		if err != nil {
			r.log.Errorf(
				"Stat single dir %s in %s in loongstore quota failed: %v",
				path, volume, err)
			continue
		}
		rQuotas = append(rQuotas, quota)
	}
	if len(rQuotas) == 0 {
		return nil, errV1.ErrorQuotaOperateFailed("stat paths failed.")
	}
	return rQuotas, nil
}

func (r *loongstoreRepo) UnsetQuota(
	ctx context.Context, quota *model.Quota) error {

	lQuota, err := NewLoongStoreQuotaFromModel(quota)
	if err != nil {
		return err
	}

	// 0. check quota existance
	originQuota, err := r.querySingleQuota(ctx, lQuota)
	if err != nil {
		return err
	}
	if originQuota == nil {
		return errV1.ErrorQuotaOperateFailed("No quota found when UnsetQuota!")
	}

	// has quota, update or delete
	switch quota.Type {
	case pb.Quota_TYPE_USAGE:
		originQuota.Size = 0
	case pb.Quota_TYPE_OBJECTS:
		originQuota.Number = 0
	case pb.Quota_TYPE_UNKNOWN:
		fallthrough
	default:
		return errV1.ErrorQuotaInvalidType(quota.Type.String())
	}

	if originQuota.Size == 0 && originQuota.Number == 0 {
		// delete
		return r.deleteQuota(ctx, originQuota)
	}

	// update
	return r.updateQuota(ctx, originQuota)
}

func (r *loongstoreRepo) RemoveQuotaDir(
	ctx context.Context, quota *model.Quota) error {

	ep := fmt.Sprintf("%s://%s:%d/api/store/fs/file/remove",
		r.c.Protocal, r.c.Host, r.c.Port)
	rawUrl, err := url.Parse(ep)
	if err != nil {
		return errV1.ErrorUnknown(err.Error())
	}

	urlParams := url.Values{}
	urlParams.Set("access_token", r.accessToken)
	rawUrl.RawQuery = urlParams.Encode()
	urlStr := rawUrl.String()

	bodyParams := url.Values{}
	path := fmt.Sprintf("/%s%s", quota.Volume, quota.Path)
	pathJArray, err := json.Marshal([]string{path})
	if err != nil {
		return errV1.ErrorUnknown(err.Error())
	}
	bodyParams.Set("path", string(pathJArray))
	bodyParams.Set("force", "true")
	bodyParams.Set("forceDelete", "true")
	req, err := http.NewRequest("POST", urlStr, strings.NewReader(bodyParams.Encode()))
	if err != nil {
		return errV1.ErrorUnknown("create request failed: %v", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("globalResourcePoolName", r.c.GlobalResourcePool)
	r.lock.Lock()
	res, err := r.client.Do(req)
	r.lock.Unlock()
	if err != nil {
		return errV1.ErrorNetworkError(err.Error())
	}

	if res.StatusCode != 200 {
		if res.StatusCode == 410 {
			go r.updateAccessToken()
		}
		return errV1.ErrorQuotaOperateFailed(
			"RemoveQuotaDir through api failed with http code: %d", res.StatusCode)
	}

	// check return value
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return errV1.ErrorQuotaOperateFailed(
			"read RemoveQuotaDir response failed: %v", err)
	}
	js, err := sjson.NewJson(body)
	if err != nil {
		return errV1.ErrorQuotaOperateFailed(
			"parse RemoveQuotaDir response into json error: %v", err)
	}

	if errType, ok := js.CheckGet(RESPONSE_ERROR_KEY); ok {
		errStr := errType.MustString()
		if strings.Contains(errStr, PATH_NOT_EXIST) {
			return nil
		}
		if msg, err := js.Get(RESPONSE_ERROR_MSG).String(); err != nil {
			return errV1.ErrorQuotaOperateFailed(
				"RemoveQuotaDir failed, err: %s, msg get failed: %s",
				errStr, string(body))
		} else {
			return errV1.ErrorQuotaOperateFailed(
				"RemoveQuotaDir failed, err: %s, msg: %s", errStr, msg)
		}
	}

	return nil
}

func (r *loongstoreRepo) Mv(ctx context.Context, volume, src, dst string) error {
	// if cross quota
	srcPath := fmt.Sprintf("/%s%s", volume, src)
	dstPath := fmt.Sprintf("/%s%s", volume, dst)
	srcQuota, err := r.getQuotaFromPath(ctx, srcPath)
	if err != nil {
		return err
	}
	dstQuota, err := r.getQuotaFromPath(ctx, dstPath)
	if err != nil {
		return err
	}

	bothNil := srcQuota == nil && dstQuota == nil
	sameQuota := (srcQuota != nil && dstQuota != nil &&
		srcQuota.Path == dstQuota.Path)
	srcMntPath := fmt.Sprintf("%s%s", r.mntPath, src)
	dstMntPath := fmt.Sprintf("%s%s", r.mntPath, dst)
	if bothNil || sameQuota {
		cmd := exec.CommandContext(ctx, "mv", srcMntPath, dstMntPath)
		var out, errOut bytes.Buffer
		cmd.Stdout = &out
		cmd.Stderr = &errOut
		err := cmd.Run()
		if err != nil {
			return errV1.ErrorFileOperateError(
				"mv %s to %s failed: %v, errStr=%s, outStr=%s",
				srcMntPath, dstMntPath, err, errOut.String(), out.String())
		}
		return nil
	}

	// cross quota, file or folder
	srcInfo, errSrc := os.Stat(srcMntPath)
	if errSrc != nil {
		return errV1.ErrorFileOperateError(
			"Stat src file %s failed: %v", srcMntPath, errSrc)
	}
	dstInfo, errDst := os.Stat(dstMntPath)
	if errDst != nil && !strings.Contains(
		strings.ToLower(errDst.Error()), strings.ToLower(utils.PATH_NO_FOUND)) {
		return errV1.ErrorFileOperateError(
			"Stat dst file %s failed: %v", dstMntPath, errDst)
	}
	if srcInfo.IsDir() {
		cpDstPath := dstMntPath
		if errDst == nil {
			cpDstPath += "/"
		}
		// folder, folder to new name, cp and delete
		cmd := exec.CommandContext(ctx, "cp", "-afr", srcMntPath, cpDstPath)
		var out, errOut bytes.Buffer
		cmd.Stdout = &out
		cmd.Stderr = &errOut
		err = cmd.Run()
		if err != nil {
			return errV1.ErrorFileOperateError(
				"cp %s to %s failed: %v, errStr=%s, outStr=%s",
				srcMntPath, dstMntPath, err, errOut.String(), out.String())
		}
		err = os.RemoveAll(srcMntPath)
		if err != nil {
			return errV1.ErrorFileOperateError(
				"remove %s failed: %v", srcMntPath, err)
		}
		return nil
	}

	// source is file
	if errDst == nil && !dstInfo.IsDir() {
		return errV1.ErrorFileOperateError(
			"Cound't mv src file %s to an existed file %s.", src, dst)
	}

	// errDst == nil, and path_not_found
	// file, file to new name
	targetLink := dstMntPath
	if errDst == nil && dstInfo.IsDir() {
		// file, file to new folder, ln, delete
		targetLink = fmt.Sprintf("%s/%s", dstMntPath, srcInfo.Name())
	}
	err = os.Link(srcMntPath, targetLink)
	if err != nil {
		return errV1.ErrorFileOperateError(
			"hard link %s to %s failed: %v", srcMntPath, targetLink, err)
	}
	err = os.RemoveAll(srcMntPath)
	if err != nil {
		return errV1.ErrorFileOperateError(
			"remove %s failed: %v", srcMntPath, err)
	}
	return nil
}

func (r *loongstoreRepo) mkdir(ctx context.Context, quota *model.Quota) error {

	ep := fmt.Sprintf("%s://%s:%d/api/store/fs/file/mkdir",
		r.c.Protocal, r.c.Host, r.c.Port)
	rawUrl, err := url.Parse(ep)
	if err != nil {
		return errV1.ErrorUnknown(err.Error())
	}

	urlParams := url.Values{}
	urlParams.Set("access_token", r.accessToken)
	rawUrl.RawQuery = urlParams.Encode()
	urlStr := rawUrl.String()

	bodyParams := url.Values{}
	path := fmt.Sprintf("/%s%s", quota.Volume, quota.Path)
	bodyParams.Set("path", path)

	req, err := http.NewRequest("POST", urlStr, strings.NewReader(bodyParams.Encode()))
	if err != nil {
		return errV1.ErrorUnknown("create request failed: %v", err)
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("globalResourcePoolName", r.c.GlobalResourcePool)

	r.lock.Lock()
	res, err := r.client.Do(req)
	r.lock.Unlock()
	if err != nil {
		return errV1.ErrorNetworkError(err.Error())
	}
	if res.StatusCode != 200 {
		if res.StatusCode == 410 {
			go r.updateAccessToken()
		}
		return errV1.ErrorQuotaOperateFailed(
			"mkdir through api failed with http code: %d", res.StatusCode)
	}

	// check return value
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return errV1.ErrorQuotaOperateFailed("read mkdir response failed: %v", err)
	}
	js, err := sjson.NewJson(body)
	if err != nil {
		return errV1.ErrorQuotaOperateFailed(
			"parse mkdir response into json error: %v", err)
	}

	if errType, ok := js.CheckGet(RESPONSE_ERROR_KEY); ok {
		errStr := errType.MustString()
		if strings.Contains(errStr, PATH_EXIST) {
			return nil
		}
		if msg, err := js.Get(RESPONSE_ERROR_MSG).String(); err != nil {
			return errV1.ErrorQuotaOperateFailed(
				"mkdir failed, err: %s, msg get failed: %s",
				errStr, string(body))
		} else {
			return errV1.ErrorQuotaOperateFailed(
				"mkdir failed, err: %s, msg: %s", errStr, msg)
		}
	}

	return nil
}

func (r *loongstoreRepo) listDir(volume, path string) ([]string, error) {

	ep := fmt.Sprintf("%s://%s:%d/api/store/fs/file/folders",
		r.c.Protocal, r.c.Host, r.c.Port)
	rawUrl, err := url.Parse(ep)
	if err != nil {
		return nil, errV1.ErrorUnknown(err.Error())
	}

	urlParams := url.Values{}
	urlParams.Set("access_token", r.accessToken)
	rawUrl.RawQuery = urlParams.Encode()
	urlStr := rawUrl.String()

	bodyParams := url.Values{}
	internalPath := fmt.Sprintf("/%s%s", volume, path)
	bodyParams.Set("path", internalPath)

	req, err := http.NewRequest("POST", urlStr, strings.NewReader(bodyParams.Encode()))
	if err != nil {
		return nil, errV1.ErrorUnknown("create request failed: %v", err)
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("globalResourcePoolName", r.c.GlobalResourcePool)

	r.lock.Lock()
	res, err := r.client.Do(req)
	r.lock.Unlock()
	if err != nil {
		return nil, errV1.ErrorNetworkError(err.Error())
	}
	if res.StatusCode != 200 {
		if res.StatusCode == 410 {
			go r.updateAccessToken()
		}
		return nil, errV1.ErrorQuotaOperateFailed(
			"listdir through api failed with http code: %d", res.StatusCode)
	}

	// check return value
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, errV1.ErrorQuotaOperateFailed(
			"read listdir response failed: %v", err)
	}
	js, err := sjson.NewJson(body)
	if err != nil {
		return nil, errV1.ErrorQuotaOperateFailed(
			"parse listdir response into json error: %v", err)
	}

	if errType, ok := js.CheckGet(RESPONSE_ERROR_KEY); ok {
		errStr := errType.MustString()
		if msg, err := js.Get(RESPONSE_ERROR_MSG).String(); err != nil {
			return nil, errV1.ErrorQuotaOperateFailed(
				"listdir failed, err: %s, msg get failed: %s",
				errStr, string(body))
		} else {
			return nil, errV1.ErrorQuotaOperateFailed(
				"listdir failed, err: %s, msg: %s", errStr, msg)
		}
	}

	// no error, parse to response
	rItems := make([]string, 0)
	jItems := js.Get(QUOTA_DATA_KEY).MustArray()
	for _, item := range jItems {
		rItems = append(rItems, item.(string))
	}
	return rItems, nil
}

func (r *loongstoreRepo) createQuota(
	ctx context.Context, quota *LoongStoreQuota) error {

	ep := fmt.Sprintf("%s://%s:%d/api/store/file/quota/addFileDirQuota",
		r.c.Protocal, r.c.Host, r.c.Port)
	rawUrl, err := url.Parse(ep)
	if err != nil {
		return errV1.ErrorUnknown(err.Error())
	}

	urlParams := url.Values{}
	urlParams.Set("access_token", r.accessToken)
	rawUrl.RawQuery = urlParams.Encode()
	urlStr := rawUrl.String()

	dataByte, err := json.Marshal([]*LoongStoreQuota{quota})
	if err != nil {
		return errV1.ErrorQuotaOperateFailed(err.Error())
	}
	bodyReader := bytes.NewReader(dataByte)

	req, err := http.NewRequest("POST", urlStr, bodyReader)
	if err != nil {
		return errV1.ErrorUnknown("create request failed: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("globalResourcePoolName", r.c.GlobalResourcePool)
	r.lock.Lock()
	res, err := r.client.Do(req)
	r.lock.Unlock()
	if err != nil {
		return errV1.ErrorNetworkError(err.Error())
	}

	if res.StatusCode != 200 {
		if res.StatusCode == 410 {
			go r.updateAccessToken()
		}
		return errV1.ErrorQuotaOperateFailed(
			"setquota through api failed with http code: %d", res.StatusCode)
	}
	// check return value
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return errV1.ErrorQuotaOperateFailed(
			"read setquota response failed: %v", err)
	}
	js, err := sjson.NewJson(body)
	if err != nil {
		return errV1.ErrorQuotaOperateFailed(
			"parse setquota response into json error: %v", err)
	}

	if errType, ok := js.CheckGet(RESPONSE_ERROR_KEY); ok {
		errStr := errType.MustString()
		if msg, err := js.Get(RESPONSE_ERROR_MSG).String(); err != nil {
			return errV1.ErrorQuotaOperateFailed(
				"setquota failed, err: %s, msg get failed: %s",
				errStr, string(body))
		} else {
			return errV1.ErrorQuotaOperateFailed(
				"setquota failed, err: %s, msg: %s", errStr, msg)
		}
	}

	return nil
}

func (r *loongstoreRepo) deleteQuota(
	ctx context.Context, quota *LoongStoreQuota) error {

	ep := fmt.Sprintf("%s://%s:%d/api/store/file/quota/deleteQuota",
		r.c.Protocal, r.c.Host, r.c.Port)
	rawUrl, err := url.Parse(ep)
	if err != nil {
		return errV1.ErrorUnknown(err.Error())
	}

	urlParams := url.Values{}
	urlParams.Set("access_token", r.accessToken)
	rawUrl.RawQuery = urlParams.Encode()
	urlStr := rawUrl.String()

	bodyMap := make(map[string]interface{})
	bodyMap["path"] = quota.Path
	bodyMap["type"] = "Dir"
	bodyMap["mid"] = quota.Mid
	bodyMap["id"] = quota.Qid
	bodyMap["dirino"] = quota.Dirino
	bodySlice := []map[string]interface{}{bodyMap}
	dataByte, err := json.Marshal(bodySlice)
	if err != nil {
		return errV1.ErrorQuotaOperateFailed(err.Error())
	}
	bodyReader := bytes.NewReader(dataByte)

	req, err := http.NewRequest("POST", urlStr, bodyReader)
	if err != nil {
		return errV1.ErrorUnknown("create request failed: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("globalResourcePoolName", r.c.GlobalResourcePool)
	r.lock.Lock()
	res, err := r.client.Do(req)
	r.lock.Unlock()
	if err != nil {
		return errV1.ErrorNetworkError("deleteQuota through api failed: %v", err)
	}
	if res.StatusCode != 200 {
		if res.StatusCode == 410 {
			go r.updateAccessToken()
		}
		return errV1.ErrorQuotaOperateFailed(
			"deleteQuota through api failed with http code: %d",
			res.StatusCode)
	}

	// check return value
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return errV1.ErrorQuotaOperateFailed(
			"read deleteQuota response body failed: %v", err)
	}
	js, err := sjson.NewJson(body)
	if err != nil {
		return errV1.ErrorQuotaOperateFailed(
			"parse deleteQuota response into json error: %v", err)
	}

	if errType, ok := js.CheckGet(RESPONSE_ERROR_KEY); ok {
		errStr := errType.MustString()
		if msg, err := js.Get(RESPONSE_ERROR_MSG).String(); err != nil {
			return errV1.ErrorQuotaOperateFailed(
				"deleteQuota failed, err: %s, msg get failed: %s",
				errStr, string(body))
		} else {
			return errV1.ErrorQuotaOperateFailed(
				"deleteQuota failed, err: %s, msg: %s", errStr, msg)
		}
	}

	return nil
}

func (r *loongstoreRepo) setQuotaThreshold(
	ctx context.Context, q *LoongStoreQuota) error {

	ep := fmt.Sprintf("%s://%s:%d/api/store/file/quota/alarmRadio/set",
		r.c.Protocal, r.c.Host, r.c.Port)
	rawUrl, err := url.Parse(ep)
	if err != nil {
		return errV1.ErrorUnknown(err.Error())
	}

	urlParams := url.Values{}
	urlParams.Set("access_token", r.accessToken)
	urlParams.Set("mid", strconv.FormatInt(q.Mid, 10))
	urlParams.Set("qid", strconv.FormatInt(q.Qid, 10))
	urlParams.Set("alarmFileNumRadio", strconv.FormatFloat(
		float64(q.AlarmFileNumRadio), 'f', 4, 64))
	urlParams.Set("alarmSizeRadio", strconv.FormatFloat(
		float64(q.AlarmSizeRadio), 'f', 4, 64))
	rawUrl.RawQuery = urlParams.Encode()
	urlStr := rawUrl.String()

	req, err := http.NewRequest("GET", urlStr, nil)
	if err != nil {
		return errV1.ErrorUnknown("create request failed: %v", err)
	}

	req.Header.Set("globalResourcePoolName", r.c.GlobalResourcePool)
	r.lock.Lock()
	res, err := r.client.Do(req)
	r.lock.Unlock()
	if err != nil {
		return errV1.ErrorNetworkError(
			"setQuotaThreshold through api failed: %v", err)
	}
	if res.StatusCode != 200 {
		if res.StatusCode == 410 {
			go r.updateAccessToken()
		}
		return errV1.ErrorQuotaOperateFailed(
			"setQuotaThreshold through api failed with http code: %d",
			res.StatusCode)
	}

	// check return value
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return errV1.ErrorQuotaOperateFailed(
			"read setQuotaThreshold response body failed: %v", err)
	}
	js, err := sjson.NewJson(body)
	if err != nil {
		return errV1.ErrorQuotaOperateFailed(
			"parse setQuotaThreshold response into json error: %v", err)
	}

	if errType, ok := js.CheckGet(RESPONSE_ERROR_KEY); ok {
		errStr := errType.MustString()
		if msg, err := js.Get(RESPONSE_ERROR_MSG).String(); err != nil {
			return errV1.ErrorQuotaOperateFailed(
				"setQuotaThreshold failed, err: %s, msg get failed: %s",
				errStr, string(body))
		} else {
			return errV1.ErrorQuotaOperateFailed(
				"setQuotaThreshold failed, err: %s, msg: %s", errStr, msg)
		}
	}

	return nil
}

func (r *loongstoreRepo) querySingleQuota(
	ctx context.Context, quota *LoongStoreQuota) (*LoongStoreQuota, error) {

	ep := fmt.Sprintf("%s://%s:%d/api/store/file/quota/querySingleQuotaInfo",
		r.c.Protocal, r.c.Host, r.c.Port)
	rawUrl, err := url.Parse(ep)
	if err != nil {
		return nil, errV1.ErrorUnknown(err.Error())
	}

	urlParams := url.Values{}
	urlParams.Set("access_token", r.accessToken)
	rawUrl.RawQuery = urlParams.Encode()
	urlStr := rawUrl.String()

	bodyParams := url.Values{}
	bodyParams.Set("path", quota.Path)
	req, err := http.NewRequest("POST", urlStr, strings.NewReader(bodyParams.Encode()))
	if err != nil {
		return nil, errV1.ErrorUnknown("create request failed: %v", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("globalResourcePoolName", r.c.GlobalResourcePool)
	r.lock.Lock()
	res, err := r.client.Do(req)
	r.lock.Unlock()
	if err != nil {
		return nil, errV1.ErrorNetworkError(
			"querySingleQuota through api failed: %v", err)
	}
	if res.StatusCode != 200 {
		if res.StatusCode == 410 {
			go r.updateAccessToken()
		}
		return nil, errV1.ErrorQuotaOperateFailed(
			"querySingleQuota through api failed with http code: %d",
			res.StatusCode)
	}

	// check return value
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, errV1.ErrorQuotaOperateFailed(
			"read querySingleQuota response failed: %v", err)
	}
	js, err := sjson.NewJson(body)
	if err != nil {
		return nil, errV1.ErrorQuotaOperateFailed(
			"parse querySingleQuota response into json error: %v", err)
	}

	if errType, ok := js.CheckGet(RESPONSE_ERROR_KEY); ok {
		errStr := errType.MustString()
		if !strings.Contains(errStr, PATH_EXIST) {
			if msg, err := js.Get(RESPONSE_ERROR_MSG).String(); err != nil {
				return nil, errV1.ErrorQuotaOperateFailed(
					"mkdir failed, err: %s, msg get failed: %s",
					errStr, string(body))
			} else {
				return nil, errV1.ErrorQuotaOperateFailed(
					"mkdir failed, err: %s, msg: %s", errStr, msg)
			}
		}
	}

	// no error, get quota info
	lQutoas, err := js.Get(QUOTA_DATA_KEY).Array()
	if err != nil {
		return nil, errV1.ErrorQuotaOperateFailed(
			"get quotadata from querySingleQuota response error: %v", err)
	}
	if len(lQutoas) == 0 {
		return nil, nil
	}

	return NewLoongStoreQuotaFromJson(js.Get(QUOTA_DATA_KEY).GetIndex(0))
}

func (r *loongstoreRepo) updateQuota(
	ctx context.Context, quota *LoongStoreQuota) error {

	ep := fmt.Sprintf("%s://%s:%d/api/store/file/quota/changeQuota",
		r.c.Protocal, r.c.Host, r.c.Port)
	rawUrl, err := url.Parse(ep)
	if err != nil {
		return errV1.ErrorUnknown(err.Error())
	}

	urlParams := url.Values{}
	urlParams.Set("access_token", r.accessToken)
	rawUrl.RawQuery = urlParams.Encode()
	urlStr := rawUrl.String()

	dataByte, err := json.Marshal([]*LoongStoreQuota{quota})
	if err != nil {
		return errV1.ErrorQuotaOperateFailed(err.Error())
	}
	bodyReader := bytes.NewReader(dataByte)
	req, err := http.NewRequest("POST", urlStr, bodyReader)
	if err != nil {
		return errV1.ErrorUnknown("create request failed: %v", err)
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("globalResourcePoolName", r.c.GlobalResourcePool)

	r.lock.Lock()
	res, err := r.client.Do(req)
	r.lock.Unlock()
	if err != nil {
		return errV1.ErrorNetworkError(
			"updateQuota through api failed: %v", err)
	}
	if res.StatusCode != 200 {
		if res.StatusCode == 410 {
			go r.updateAccessToken()
		}
		return errV1.ErrorQuotaOperateFailed(
			"updateQuota through api failed with http code: %d",
			res.StatusCode)
	}

	// check return value
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return errV1.ErrorQuotaOperateFailed(
			"read updateQuota response body failed: %v", err)
	}
	js, err := sjson.NewJson(body)
	if err != nil {
		return errV1.ErrorQuotaOperateFailed(
			"parse updateQuota response into json error: %v", err)
	}

	if errType, ok := js.CheckGet(RESPONSE_ERROR_KEY); ok {
		errStr := errType.MustString()
		if msg, err := js.Get(RESPONSE_ERROR_MSG).String(); err != nil {
			return errV1.ErrorQuotaOperateFailed(
				"updateQuota failed, err: %s, msg get failed: %s",
				errStr, string(body))
		} else {
			return errV1.ErrorQuotaOperateFailed(
				"updateQuota failed, err: %s, msg: %s", errStr, msg)
		}
	}

	return nil
}

// query all quotas in volume v.
// if v is empty, the query all quotas in all volume.
func (r *loongstoreRepo) queryAllQuotas(v string) ([]*LoongStoreQuota, error) {

	ep := fmt.Sprintf("%s://%s:%d/api/store/file/quota/showQuotaInfo",
		r.c.Protocal, r.c.Host, r.c.Port)
	rawUrl, err := url.Parse(ep)
	if err != nil {
		return nil, errV1.ErrorUnknown(err.Error())
	}

	rQuotas := make([]*LoongStoreQuota, 0)
	var curPage, pageSize int64 = 1, 100
	for {
		urlParams := url.Values{}
		urlParams.Set("access_token", r.accessToken)
		urlParams.Set("pageSize", strconv.FormatInt(pageSize, 10))
		urlParams.Set("pageNum", strconv.FormatInt(curPage, 10))
		if v != "" {
			urlParams.Set("path", "/"+v)
		}
		rawUrl.RawQuery = urlParams.Encode()
		urlStr := rawUrl.String()

		req, err := http.NewRequest("GET", urlStr, nil)
		if err != nil {
			r.log.Errorf("create request failed: %v", err)
			continue
		}
		req.Header.Set("globalResourcePoolName", r.c.GlobalResourcePool)
		r.lock.Lock()
		res, err := r.client.Do(req)
		r.lock.Unlock()
		if err != nil {
			return rQuotas, errV1.ErrorQuotaOperateFailed(
				"queryAllQuotas through api failed: %v", err)
		}
		if res.StatusCode != 200 {
			if res.StatusCode == 410 {
				go r.updateAccessToken()
			}
			return rQuotas, errV1.ErrorQuotaOperateFailed(
				"queryAllQuotas through api failed with http code: %d",
				res.StatusCode)
		}

		// check return value
		body, err := io.ReadAll(res.Body)
		if err != nil {
			return rQuotas, errV1.ErrorQuotaOperateFailed(
				"read queryAllQuotas response body failed: %v", err)
		}
		js, err := sjson.NewJson(body)
		if err != nil {
			return rQuotas, errV1.ErrorQuotaOperateFailed(
				"parse queryAllQuotas response into json error: %v", err)
		}

		if errType, ok := js.CheckGet(RESPONSE_ERROR_KEY); ok {
			errStr := errType.MustString()
			if msg, err := js.Get(RESPONSE_ERROR_MSG).String(); err != nil {
				return rQuotas, errV1.ErrorQuotaOperateFailed(
					"queryAllQuotas failed, err: %s, msg get failed: %s",
					errStr, string(body))
			} else {
				return rQuotas, errV1.ErrorQuotaOperateFailed(
					"queryAllQuotas failed, err: %s, msg: %s", errStr, msg)
			}
		}

		// correrct data, parse
		quotaJArray := js.Get(QUOTA_DATA_KEY).Get("rows").MustArray()
		for idx := range quotaJArray {
			quotaJson := js.Get(QUOTA_DATA_KEY).Get("rows").GetIndex(idx)
			tQuota, err := NewLoongStoreQuotaFromJson(quotaJson)
			if err != nil {
				r.log.Errorf("NewLoongStoreQuotaFromJson faild: %v", err)
				continue
			}
			rQuotas = append(rQuotas, tQuota)
		}

		curPage += 1
		res.Body.Close()
		if int64(len(quotaJArray)) < pageSize {
			break
		}
	}

	return rQuotas, nil
}

func (r *loongstoreRepo) statSingleDir(
	volume string, quotaType pb.Quota_Type, path string) (
	*model.Quota, error) {

	var used int64
	var err error
	absPath := fmt.Sprintf("%s%s", r.mntPath, path)
	switch quotaType {
	case pb.Quota_TYPE_USAGE:
		used, err = utils.DiskUsage(absPath, utils.DISK_USAGE_TIMEOUT)
		if err != nil {
			err = errV1.ErrorFileOperateError("disk usage failed: %v", err)
		}
	case pb.Quota_TYPE_OBJECTS:
		used, err = utils.ObjectsCount(absPath, utils.DISK_USAGE_TIMEOUT)
		if err != nil {
			err = errV1.ErrorFileOperateError("objects count failed: %v", err)
		}
	case pb.Quota_TYPE_UNKNOWN:
		fallthrough
	default:
		used, err = 0, errV1.ErrorQuotaInvalidType(quotaType.String())
	}

	if err != nil {
		return nil, err
	}

	rQuota := &model.Quota{
		Volume:  volume,
		Type:    quotaType,
		Path:    path,
		Size:    -1,
		Percent: 0,
		Used:    used,
	}
	return rQuota, nil
}

func (r *loongstoreRepo) updateAccessToken() {
	token, err := getAccessToken(r.client, r.c)
	if err != nil {
		r.log.Errorf("update access token failed when getAccessToken: %v", err)
		return
	}
	r.log.Infof("updated accessToken: %s", token)
	r.accessToken = token
}

func (r *loongstoreRepo) getQuotaFromPath(ctx context.Context, path string) (
	*LoongStoreQuota, error) {

	segs := strings.Split(path, "/")
	for i := len(segs); i >= 2; i-- {
		qQuota := &LoongStoreQuota{
			Path: strings.Join(segs[:i], "/"),
		}
		rQuota, err := r.querySingleQuota(ctx, qQuota)
		if err != nil {
			return nil, err
		}
		if rQuota != nil {
			return rQuota, nil
		}
	}
	return nil, nil
}

func getAccessToken(client *http.Client, c *conf.LoongStore) (string, error) {

	ep := fmt.Sprintf("%s://%s:%d/oauth/token", c.Protocal, c.Host, c.Port)
	rawUrl, err := url.Parse(ep)
	if err != nil {
		return "", err
	}
	cipherPwd, err := utils.EncryptWithCert(c.Cert, []byte(c.Pwd))
	if err != nil {
		return "", err
	}

	urlStr := rawUrl.String()
	bodyParams := url.Values{}
	bodyParams.Set("grant_type", "password")
	bodyParams.Set("client_secret", CLIENT_SECRET)
	bodyParams.Set("client_id", "manager")
	bodyParams.Set("username", c.UserName)
	bodyParams.Set("password", cipherPwd)
	res, err := client.PostForm(urlStr, bodyParams)
	if err != nil {
		return "", err
	}

	defer res.Body.Close()
	js, err := sjson.NewFromReader(res.Body)
	if err != nil {
		return "", err
	}

	token, err := js.Get("value").String()
	return token, err
}

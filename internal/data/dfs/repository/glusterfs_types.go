package repository

import (
	"encoding/xml"
	"strconv"
	"strings"

	pb "git-plat.tecorigin.net/ai-platform/metadata-middleware/api/md-middleware/v1"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/model"
)

type GlusterQuota struct {
	XMLName xml.Name `xml:"limit"`
	Path    string   `xml:"path"`
	Size    string   `xml:"hard_limit"`
	Percent string   `xml:"soft_limit_percent"`
	Used    string   `xml:"used_space"`
}

func (q GlusterQuota) QuotaModle(v string, t pb.Quota_Type) (*model.Quota, error) {
	var err error

	var percent int64
	if q.Percent != "N/A" {
		pStr := strings.Trim(q.Percent, "%")
		percent, err = strconv.ParseInt(pStr, 10, 64)
		if err != nil {
			return nil, err
		}
	}

	var size int64 = -1
	if q.Size != "N/A" {
		size, err = strconv.ParseInt(q.<PERSON>, 10, 64)
		if err != nil {
			return nil, err
		}
	}

	var used int64 = -1
	if q.Used != "N/A" {
		used, err = strconv.ParseInt(q.Used, 10, 64)
		if err != nil {
			return nil, err
		}
	}

	return &model.Quota{
		Volume:  v,
		Type:    t,
		Path:    q.Path,
		Size:    size,
		Used:    used,
		Percent: int32(percent),
	}, nil
}

type GlusterQuotas struct {
	XMLName xml.Name       `xml:"cliOutput"`
	Items   []GlusterQuota `xml:"volQuota>limit"`
}

const QUOTA_PATH_NO_FOUND = "No such file or directory"

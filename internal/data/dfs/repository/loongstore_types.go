package repository

import (
	"fmt"
	"strings"

	"github.com/bitly/go-simplejson"
	"github.com/imdario/mergo"

	errV1 "git-plat.tecorigin.net/ai-platform/errno/api/err/v1"
	pb "git-plat.tecorigin.net/ai-platform/metadata-middleware/api/md-middleware/v1"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/model"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/utils"
)

const (
	CLIENT_SECRET  = "MCLdoqbydByzAbOC"
	QUOTA_DATA_KEY = "data"

	PATH_EXIST     = "path_exist"
	PATH_NOT_EXIST = "path_not_exist"

	QUOTA_EXIST = "quota_exists"

	RESPONSE_ERROR_KEY = "error"
	RESPONSE_ERROR_MSG = "error_message"

	// 异步设置quota的延迟时间, 取决于后台存储空配额的建立时间
	ASYNC_QUOTA_DELAY_SECONDS = 60
)

type LoongStoreQuota struct {
	Path     string `json:"path"`     //路径 /path 代表实际路径 /datapool/path
	Size     int64  `json:"maxsize"`  //最大容量（非负整数,0表示无限制）
	SizeType string `json:"sizeType"` //容量单位（MB,GB,TB,PB）
	Soft     bool   `json:"soft"`     //是否设置为软配额，软配额可以写超，无限制。超出后web的告警信息会有警报声音(web可以关闭声音)。！！！，最好false。超出设定的比例值也会报警。
	// 资源池版本了，默认为异步
	// Async    bool   `json:"async"`      //是否异步执行,空目录同步false, 目录已经有数据，使用异步
	Number int64 `json:"maxfilenum"` //最大文件数量（0-1000000000000000,0表示无限制）

	Mid               int64   `json:"mid"`
	Qid               int64   `json:"qid"`
	Dirino            int64   `json:"dirino"`
	AlarmSizeRadio    float64 `json:"-"`
	AlarmFileNumRadio float64 `json:"-"`
	CurSize           int64   `json:"-"`
	CurNumber         int64   `json:"-"`
}

func NewLoongStoreQuotaFromModel(quota *model.Quota) (*LoongStoreQuota, error) {
	r := LoongStoreQuota{
		Path: fmt.Sprintf("/%s%s", quota.Volume, quota.Path),
		Soft: false,
	}

	switch quota.Type {
	case pb.Quota_TYPE_USAGE:
		r.Number = 0
		r.SizeType = "MB"
		if quota.Size == -1 {
			r.Size = 0
		} else if quota.Size == 0 {
			r.Size = 1
		} else {
			r.Size = quota.Size / 1024 / 1024
		}
		r.AlarmSizeRadio = float64(quota.Percent) / 100.0
	case pb.Quota_TYPE_OBJECTS:
		r.Size = 0
		r.SizeType = "MB"
		if quota.Size == -1 {
			r.Number = 0
		} else if quota.Size == 0 {
			r.Number = 1
		} else {
			r.Number = quota.Size
		}
		r.AlarmFileNumRadio = float64(quota.Percent) / 100.0
	case pb.Quota_TYPE_UNKNOWN:
		fallthrough
	default:
		return nil, errV1.ErrorQuotaInvalidType(quota.Type.String())
	}

	return &r, nil
}

/*
loongstore quota in json,  exmaple

	{
		"id": 12,
		"mid": 1,
		"type": "Dir",
		"qid": 3,
		"path": "/defaultClient/yyy",
		"dirino": 16812119297,
		"name": null,
		"cursize": 4104192,
		"maxsize": 1293942784,
		"curfiledirs": 1,
		"maxfiledirs": 1111,
		"uid_gid_ino": 0,
		"createTime": 1715694661346,
		"alarmRadio": 0.8,
		"alarmFileNumRadio": 0.8,
		"alarmSizeRadio": 0.8,
		"applicationType": null,
		"soft": false,
		"lastModifyDate": 0
	}
*/
func NewLoongStoreQuotaFromJson(obj *simplejson.Json) (*LoongStoreQuota, error) {

	pathStr, err := obj.Get("path").String()
	if err != nil {
		return nil, errV1.ErrorQuotaOperateFailed(
			"parse path from json failed: %v", err)
	}

	maxSize, err := obj.Get("maxsize").Int64()
	if err != nil {
		return nil, errV1.ErrorQuotaOperateFailed(
			"parse maxSize from json failed: %v", err)
	}

	softBool, err := obj.Get("soft").Bool()
	if err != nil {
		return nil, errV1.ErrorQuotaOperateFailed(
			"parse softBool from json failed: %v", err)
	}

	maxFileDirs, err := obj.Get("maxfiledirs").Int64()
	if err != nil {
		return nil, errV1.ErrorQuotaOperateFailed(
			"parse maxFileDirs from json failed: %v", err)
	}

	mid, err := obj.Get("mid").Int64()
	if err != nil {
		return nil, errV1.ErrorQuotaOperateFailed(
			"parse mid from json failed: %v", err)
	}

	qid, err := obj.Get("qid").Int64()
	if err != nil {
		return nil, errV1.ErrorQuotaOperateFailed(
			"parse qid from json failed: %v", err)
	}

	dirIno, err := obj.Get("dirino").Int64()
	if err != nil {
		return nil, errV1.ErrorQuotaOperateFailed(
			"parse dirino from json failed: %v", err)
	}

	alarmSizeRadio, err := obj.Get("alarmSizeRadio").Float64()
	if err != nil {
		return nil, errV1.ErrorQuotaOperateFailed(
			"parse alarmSizeRadio from json failed: %v", err)
	}

	alarmFileNumRadio, err := obj.Get("alarmFileNumRadio").Float64()
	if err != nil {
		return nil, errV1.ErrorQuotaOperateFailed(
			"parse alarmFileNumRadio from json failed: %v", err)
	}

	curSize, err := obj.Get("cursize").Int64()
	if err != nil {
		return nil, errV1.ErrorQuotaOperateFailed(
			"parse curSize from json failed: %v", err)
	}

	curfiledirs, err := obj.Get("curfiledirs").Int64()
	if err != nil {
		return nil, errV1.ErrorQuotaOperateFailed(
			"parse curfiledirs from json failed: %v", err)
	}

	rQuota := LoongStoreQuota{
		Path:     pathStr,
		Size:     maxSize / utils.MB,
		SizeType: "MB",
		Soft:     softBool,
		Number:   maxFileDirs,

		Mid:               mid,
		Qid:               qid,
		Dirino:            dirIno,
		AlarmSizeRadio:    alarmSizeRadio,
		AlarmFileNumRadio: alarmFileNumRadio,
		CurSize:           curSize,
		CurNumber:         curfiledirs,
	}

	return &rQuota, nil
}

func (quota *LoongStoreQuota) Merge(
	newEntry interface{}, forceOverrideQuotaType pb.Quota_Type) error {
	// bug maybe: cannot merge default value field, like 5 -> 0, "a" -> ""
	newQuota, ok := newEntry.(*LoongStoreQuota)
	if !ok {
		return errV1.ErrorUnknown("Convert entry to LoongStoreQuota struct failed.")
	}

	err := mergo.Merge(quota, newQuota, mergo.WithOverride)
	if err != nil {
		return errV1.ErrorUnknown("Merge new newQuota to raw failed: %v", err)
	}

	switch forceOverrideQuotaType {
	case pb.Quota_TYPE_USAGE:
		quota.Size = newQuota.Size
	case pb.Quota_TYPE_OBJECTS:
		quota.Number = newQuota.Number
	default:
		return errV1.ErrorUnknown(
			"Unknown forceOverrideQuotaType: %s", forceOverrideQuotaType.String())
	}

	return nil
}

func (quota *LoongStoreQuota) QuotaModle(v string, t pb.Quota_Type) (
	*model.Quota, error) {

	prefix := "/" + v
	path, _ := strings.CutPrefix(quota.Path, prefix)
	rQuota := &model.Quota{
		Volume: v,
		Type:   t,
		Path:   path,
	}

	switch t {
	case pb.Quota_TYPE_USAGE:
		rQuota.Size = quota.Size
		switch quota.SizeType {
		case "KB":
			rQuota.Size *= utils.KB
		case "MB":
			rQuota.Size *= utils.MB
		case "GB":
			rQuota.Size *= utils.GB
		case "TB":
			rQuota.Size *= utils.TB
		case "PB":
			rQuota.Size *= utils.PB
		case "EB":
			rQuota.Size *= utils.EB
		default:
			return rQuota, errV1.ErrorQuotaOperateFailed(
				"unhandled capacity unit: %s", quota.SizeType)
		}
		rQuota.Percent = int32(quota.AlarmSizeRadio * 100)
		rQuota.Used = quota.CurSize
	case pb.Quota_TYPE_OBJECTS:
		rQuota.Size = quota.Number
		rQuota.Percent = int32(quota.AlarmFileNumRadio * 100)
		rQuota.Used = quota.CurNumber
	case pb.Quota_TYPE_UNKNOWN:
		fallthrough
	default:
		return rQuota, errV1.ErrorQuotaInvalidType(t.String())
	}

	if rQuota.Size == 0 {
		rQuota.Size = -1
	}
	return rQuota, nil
}

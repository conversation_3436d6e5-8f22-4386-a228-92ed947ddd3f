package repository

import (
	"testing"

	"git-plat.tecorigin.net/ai-platform/backend-lib/logz"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/conf"
	"github.com/stretchr/testify/assert"
)

func initLogger() {
	logz.Init(&logz.ServiceInfo{
		Module:         "test",
		ServiceId:      "",
		ServiceName:    "",
		ServiceVersion: "",
	})
}

func TestGPFSRepo(t *testing.T) {
	initLogger()
	r, clean, err := NewGPFSRepo(&conf.Data{
		Type:      "gpfs",
		MountPath: "/tecofs",
		Glusterfs: nil,
		Gpfs: &conf.GPFS{Ssh: &conf.SSH{
			Host:     "***********",
			Port:     65056,
			UserName: "root",
			Pwd:      "inspuR@123",
		}},
	})
	assert.Empty(t, err)
	defer clean()

	m, err := r.filesetM("tecofs")
	assert.Empty(t, err)
	t.Log(m)

	//exi, linked, err := r.filesetStat("2")
	//assert.Empty(t, err)
	//t.Log(exi)
	//t.Log(linked)

	//m, err := r.listFilesetUsage("tecofs")
	//assert.Empty(t, err)
	//t.Log(m)

	//err = r.CreateQuotaDir(context.TODO(), &model.Quota{
	//	Volume:  "tecofs",
	//	Type:    1,
	//	Path:    "/tenant-3/workspace-3",
	//	Size:    18000000000000,
	//	Percent: 100,
	//	Used:    0,
	//})
	//assert.Empty(t, err)

	//ret, err := r.ListQuota(context.TODO(), "tecofs", 1, []string{})
	//assert.Empty(t, err)
	//t.Log(ret)
}

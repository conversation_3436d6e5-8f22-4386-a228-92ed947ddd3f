package repository

import (
	"bytes"
	"context"
	"encoding/xml"
	"fmt"
	"os"
	"os/exec"
	"strings"
	"sync"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"

	errV1 "git-plat.tecorigin.net/ai-platform/errno/api/err/v1"
	pb "git-plat.tecorigin.net/ai-platform/metadata-middleware/api/md-middleware/v1"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/conf"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/data/dfs"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/model"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/utils"
)

type glusterRepo struct {
	mntPath string
	sshConn utils.SSHConnection

	lock sync.Mutex // gluster operation has global lock.
	log  *log.Helper
}

// NewGlusterRepo .
func NewGlusterRepo(mntPath string, c *conf.Glusterfs, logger log.Logger) (
	dfs.IDfsRepo, dfs.CleanupFunc, error) {

	sshConn, err := utils.NewSSHConnection(
		c.Ssh.Host, uint16(c.Ssh.Port), c.Ssh.UserName, c.Ssh.Pwd)
	if err != nil {
		rErr := errors.Wrap(
			err, "Create ssh connection failed when init gluster repo.")
		return nil, nil, rErr
	}

	cleanFunc := func() { sshConn.Close() }
	return &glusterRepo{
		mntPath: mntPath,
		sshConn: *sshConn,
		log:     log.NewHelper(logger),
	}, cleanFunc, nil
}

func (r *glusterRepo) CreateQuotaDir(
	ctx context.Context, quota *model.Quota) error {

	// mkdir
	path := fmt.Sprintf("%s%s", r.mntPath, quota.Path)
	err := utils.CreateDir(path)
	if err != nil {
		return errV1.ErrorFileOperateError(err.Error())
	}

	// set quota
	err = r.SetQuota(ctx, quota)
	if err != nil {
		return err
	}
	return nil
}

func (r *glusterRepo) SetQuota(
	ctx context.Context, quota *model.Quota) error {

	// resolving special values.
	// set to no limit
	if quota.Size == -1 {
		quotas, err := r.ListQuota(ctx, quota.Volume, quota.Type,
			[]string{quota.Path})
		if err != nil {
			return err
		}

		originQuota := quotas[0]
		if originQuota.Size == -1 {
			return nil
		}

		return r.UnsetQuota(ctx, quota)
	}

	// limit quota to zero
	if quota.Size == 0 {
		quota.Size = 1
	}

	command, err := getSetQuotaCommand(quota)
	log.Info(command)
	if err != nil {
		return err
	}
	r.lock.Lock()
	_, errStr, err := r.sshConn.SendCommands(command)
	r.lock.Unlock()
	if err == nil {
		return nil
	}
	if strings.Contains(errStr, QUOTA_PATH_NO_FOUND) {
		return errV1.ErrorFileNotFound(err.Error())
	}
	return errV1.ErrorQuotaOperateFailed(err.Error())
}

func (r *glusterRepo) ListQuota(
	ctx context.Context, volume string, quotaType pb.Quota_Type, paths []string) (
	[]*model.Quota, error) {

	command, err := getListQuotaCommand(volume, quotaType, paths)
	log.Info(command)
	if err != nil {
		return nil, err
	}

	r.lock.Lock()
	qStr, qErrStr, err := r.sshConn.SendCommands(command)
	r.lock.Unlock()
	if err != nil {
		return nil, errV1.ErrorQuotaOperateFailed(err.Error())
	}

	//conver the xml to quotas
	var quotas GlusterQuotas
	err = xml.Unmarshal([]byte(qStr), &quotas)
	if err != nil {
		return nil, errV1.ErrorQuotaParseFailed(err.Error())
	}

	rQuotas := make([]*model.Quota, len(quotas.Items))
	var errStr string
	for idx, item := range quotas.Items {
		tQuota, err := item.QuotaModle(volume, quotaType)
		if err != nil {
			errStr = fmt.Sprintf("Transform quota from xml failed: %v", err)
			log.Errorf(errStr)
			continue
		} else {
			rQuotas[idx] = tQuota
		}
	}

	if len(rQuotas) == 0 && len(paths) > 0 {
		if errStr != "" {
			return rQuotas, errV1.ErrorQuotaParseFailed(errStr)
		} else if strings.Contains(qErrStr, QUOTA_PATH_NO_FOUND) {
			return rQuotas, errV1.ErrorFileNotFound(QUOTA_PATH_NO_FOUND)
		} else {
			return rQuotas, errV1.ErrorQuotaOperateFailed("unknown error happens in backend store")
		}
	}
	return rQuotas, nil
}

func (r *glusterRepo) UnsetQuota(ctx context.Context, quota *model.Quota) error {
	command, err := getUnsetQuotaCommand(quota)
	log.Info(command)
	if err != nil {
		return err
	}
	r.lock.Lock()
	_, errStr, err := r.sshConn.SendCommands(command)
	r.lock.Unlock()
	if err == nil {
		return nil
	}

	if strings.Contains(errStr, QUOTA_PATH_NO_FOUND) {
		return errV1.ErrorFileNotFound(err.Error())
	}
	return errV1.ErrorQuotaOperateFailed(err.Error())
}

func (r *glusterRepo) RemoveQuotaDir(
	ctx context.Context, quota *model.Quota) error {

	// remove dir
	path := fmt.Sprintf("%s%s", r.mntPath, quota.Path)
	err := os.RemoveAll(path)
	if err != nil {
		return errV1.ErrorFileOperateError(err.Error())
	}
	return nil
}

func (r *glusterRepo) Mv(ctx context.Context, volume, src, dst string) error {
	srcMntPath := fmt.Sprintf("%s%s", r.mntPath, src)
	dstMntPath := fmt.Sprintf("%s%s", r.mntPath, dst)
	cmd := exec.CommandContext(ctx, "mv", srcMntPath, dstMntPath)
	var out, errOut bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &errOut
	err := cmd.Run()
	if err != nil {
		return errV1.ErrorFileOperateError(
			"mv %s to %s failed: %v, errStr=%s, outStr=%s",
			srcMntPath, dstMntPath, err, errOut.String(), out.String())
	}
	return nil
}

func getSetQuotaCommand(q *model.Quota) (string, error) {
	// gluster volume quota <VOLNAME> {limit-usage <path> <size> [<percent>]}
	cmdTemplate := "gluster volume quota %s %s %s %d"
	var command, verb string

	switch q.Type {
	case pb.Quota_TYPE_USAGE:
		verb = "limit-usage"
	case pb.Quota_TYPE_OBJECTS:
		verb = "limit-objects"
	default:
		return "", errV1.ErrorQuotaInvalidType(q.Type.String())
	}

	command = fmt.Sprintf(cmdTemplate, q.Volume, verb, q.Path, q.Size)
	if q.Percent > 0 {
		command += fmt.Sprintf(" %d", q.Percent)
	}
	return command, nil
}

func getUnsetQuotaCommand(q *model.Quota) (string, error) {
	// gluster volume quota <VOLNAME> remove <path>| remove-objects <path>
	cmdTemplate := "gluster volume quota %s %s %s"
	var command, verb string

	switch q.Type {
	case pb.Quota_TYPE_USAGE:
		verb = "remove"
	case pb.Quota_TYPE_OBJECTS:
		verb = "remove-objects"
	default:
		return "", errV1.ErrorQuotaInvalidType(q.Type.String())
	}

	command = fmt.Sprintf(cmdTemplate, q.Volume, verb, q.Path)
	return command, nil
}

func getListQuotaCommand(
	volume string, quotaType pb.Quota_Type, paths []string) (
	string, error) {
	// volume quota <VOLNAME> list [<path> ...]| list-objects [<path> ...]
	cmdTemplate := "gluster volume quota %s %s %s --xml"
	var command, verb string

	switch quotaType {
	case pb.Quota_TYPE_USAGE:
		verb = "list"
	case pb.Quota_TYPE_OBJECTS:
		verb = "list-objects"
	default:
		return "", errV1.ErrorQuotaInvalidType(quotaType.String())
	}

	pathsStr := strings.Join(paths, " ")
	command = fmt.Sprintf(cmdTemplate, volume, verb, pathsStr)
	return command, nil
}

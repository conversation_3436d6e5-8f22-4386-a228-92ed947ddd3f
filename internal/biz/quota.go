package biz

import (
	"context"
	"time"

	pb "git-plat.tecorigin.net/ai-platform/metadata-middleware/api/md-middleware/v1"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/data/dfs"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/model"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/utils"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
)

var (
	// ErrUserNotFound is user not found.
	ErrUserNotFound = errors.NotFound(pb.ErrorReason_USER_NOT_FOUND.String(), "user not found")
)

// QuotaUsecase is a Greeter usecase.
type QuotaUsecase struct {
	repo dfs.IDfsRepo
	log  *log.Helper
}

// NewQuotaUsecase new a Greeter usecase.
func NewQuotaUsecase(repo dfs.IDfsRepo, logger log.Logger) *QuotaUsecase {
	return &QuotaUsecase{
		repo: repo,
		log:  log.<PERSON><PERSON>per(logger),
	}
}

func (uc *QuotaUsecase) SetQuota(
	ctx context.Context, quota *model.Quota) error {

	uc.log.WithContext(ctx).Infof("Set quota for: %v", quota)

	err := utils.BackoffRetry(func() error {
		return uc.repo.SetQuota(ctx, quota)
	}, utils.WithMaxRetries(utils.RandomBackoff(1*time.Second, 5*time.Second), 3))

	if err != nil {
		uc.log.WithContext(ctx).Errorf("Set quota failed for %v: %v", quota, err)
		return err
	}
	uc.log.WithContext(ctx).Infof("Set quota ok for %v", quota)
	return nil
}

func (uc *QuotaUsecase) CreateQuotaDir(
	ctx context.Context, quota *model.Quota) error {

	uc.log.WithContext(ctx).Infof("Create quota directory for: %v", quota)

	err := utils.BackoffRetry(func() error {
		return uc.repo.CreateQuotaDir(ctx, quota)
	}, utils.WithMaxRetries(utils.RandomBackoff(1*time.Second, 5*time.Second), 3))

	if err != nil {
		uc.log.WithContext(ctx).Errorf(
			"Create quota directory failed for %v: %v", quota, err)
		return err
	}
	uc.log.WithContext(ctx).Infof("Create quota directory ok for %v", quota)
	return nil
}

func (uc *QuotaUsecase) ListQuota(
	ctx context.Context,
	volume string, quotaType pb.Quota_Type, paths []string) (
	[]*model.Quota, error) {

	uc.log.WithContext(ctx).Infof(
		"List %s quota in %s for: %v", quotaType.String(), volume, paths)

	var rQuotas []*model.Quota
	err := utils.BackoffRetry(func() error {
		quotas, err := uc.repo.ListQuota(ctx, volume, quotaType, paths)
		if err != nil {
			return err
		}
		rQuotas = quotas
		return nil
	}, utils.WithMaxRetries(utils.RandomBackoff(1*time.Second, 5*time.Second), 3))

	if err != nil {
		uc.log.WithContext(ctx).Errorf(
			"List %s quota in volume %s failed for %v: %v",
			quotaType.String(), volume, paths, err)
		return nil, err
	}
	uc.log.WithContext(ctx).Infof(
		"List %s quota in volume %s ok for %v", quotaType.String(), volume, paths)
	return rQuotas, nil
}

func (uc *QuotaUsecase) UnsetQuota(
	ctx context.Context, quota *model.Quota) error {

	uc.log.WithContext(ctx).Infof("Unset quota for: %v", quota)

	err := utils.BackoffRetry(func() error {
		return uc.repo.UnsetQuota(ctx, quota)
	}, utils.WithMaxRetries(utils.RandomBackoff(1*time.Second, 5*time.Second), 3))

	if err != nil {
		uc.log.WithContext(ctx).Errorf(
			"Unset quota failed for %v: %v", quota, err)
		return err
	}
	uc.log.WithContext(ctx).Infof("Unset quota ok for %v", quota)
	return nil
}

func (uc *QuotaUsecase) RemoveQuotaDir(
	ctx context.Context, quota *model.Quota) error {

	uc.log.WithContext(ctx).Infof("Remove quota directory for: %v", quota)

	err := utils.BackoffRetry(func() error {
		return uc.repo.RemoveQuotaDir(ctx, quota)
	}, utils.WithMaxRetries(utils.RandomBackoff(1*time.Second, 5*time.Second), 3))

	if err != nil {
		uc.log.WithContext(ctx).Errorf(
			"Remove quota directory failed for %v: %v", quota, err)
		return err
	}
	uc.log.WithContext(ctx).Infof("Remove quota directory ok for %v", quota)
	return nil
}

func (uc *QuotaUsecase) Mv(
	ctx context.Context, volume string, src, dst string) error {

	uc.log.WithContext(ctx).Infof(
		"Mv file %s to %s in volume %s", src, dst, volume)

	err := utils.BackoffRetry(func() error {
		return uc.repo.Mv(ctx, volume, src, dst)
	}, utils.WithMaxRetries(utils.RandomBackoff(1*time.Second, 5*time.Second), 3))

	if err != nil {
		uc.log.WithContext(ctx).Errorf(
			"Mv file %s to %s in volume %s: %v", src, dst, volume, err)
		return err
	}
	uc.log.WithContext(ctx).Infof("Mv file %s to %s in volume %s", src, dst, volume)
	return nil
}

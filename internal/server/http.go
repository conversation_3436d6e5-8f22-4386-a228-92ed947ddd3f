package server

import (
	v1 "git-plat.tecorigin.net/ai-platform/metadata-middleware/api/md-middleware/v1"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/conf"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/service"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/transport/http"
)

// NewHTTPServer new an HTTP server.
func NewHTTPServer(c *conf.Server, greeter *service.MetaDataMiddlewareService, logger log.Logger) *http.Server {
	var opts = []http.ServerOption{
		http.Middleware(
			recovery.Recovery(),
		),
	}
	if c.Http.Network != "" {
		opts = append(opts, http.Network(c.Http.Network))
	}
	if c.Http.Addr != "" {
		opts = append(opts, http.Address(c.Http.Addr))
	}
	if c.Http.Timeout != nil {
		opts = append(opts, http.Timeout(c.Http.Timeout.AsDuration()))
	}
	srv := http.NewServer(opts...)
	v1.RegisterMetaDataMiddlewareHTTPServer(srv, greeter)
	return srv
}

package service

import (
	"context"

	errV1 "git-plat.tecorigin.net/ai-platform/errno/api/err/v1"
	pb "git-plat.tecorigin.net/ai-platform/metadata-middleware/api/md-middleware/v1"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/biz"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/model"

	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

type MetaDataMiddlewareService struct {
	pb.UnimplementedMetaDataMiddlewareServer

	uc *biz.QuotaUsecase
}

func NewMetaDataMiddlewareService(
	uc *biz.QuotaUsecase) *MetaDataMiddlewareService {
	return &MetaDataMiddlewareService{uc: uc}
}

func (s *MetaDataMiddlewareService) SetQuota(
	ctx context.Context, req *pb.SetQuotaRequest) (*emptypb.Empty, error) {

	if err := req.ValidateAll(); err != nil {
		return &emptypb.Empty{}, errV1.ErrorInvalidParams(err.Error())
	}

	// form model
	quota := model.NewQuotaFromSetRequest(req)

	err := s.uc.SetQuota(ctx, quota)
	return &emptypb.Empty{}, err
}

func (s *MetaDataMiddlewareService) CreateQuotaDir(
	ctx context.Context, req *pb.SetQuotaRequest) (
	*emptypb.Empty, error) {

	if err := req.ValidateAll(); err != nil {
		return &emptypb.Empty{}, errV1.ErrorInvalidParams(err.Error())
	}

	// form model
	quota := model.NewQuotaFromSetRequest(req)

	err := s.uc.CreateQuotaDir(ctx, quota)
	return &emptypb.Empty{}, err
}

func (s *MetaDataMiddlewareService) ListQuota(
	ctx context.Context, req *pb.ListQuotaRequest) (*pb.ListQuotaReply, error) {

	if err := req.ValidateAll(); err != nil {
		return nil, errV1.ErrorInvalidParams(err.Error())
	}

	quotas, err := s.uc.ListQuota(ctx, req.Volume, req.Type, req.Paths)
	if err != nil {
		return nil, err
	}

	// transform
	rQuotas := make([]*pb.Quota, len(quotas))
	for idx, quota := range quotas {
		rQuotas[idx] = quota.PBQuota()
	}

	return &pb.ListQuotaReply{
		Quotas: rQuotas,
	}, nil
}

func (s *MetaDataMiddlewareService) UnsetQuota(
	ctx context.Context, req *pb.UnsetQuotaRequest) (*emptypb.Empty, error) {

	if err := req.ValidateAll(); err != nil {
		return nil, errV1.ErrorInvalidParams(err.Error())
	}

	// form model
	quota := model.NewQuotaFromUnsetRequest(req)

	err := s.uc.UnsetQuota(ctx, quota)
	return &emptypb.Empty{}, err
}

func (s *MetaDataMiddlewareService) RemoveQuotaDir(
	ctx context.Context, req *pb.RemoveQuotaRequest) (*emptypb.Empty, error) {

	if err := req.ValidateAll(); err != nil {
		return nil, errV1.ErrorInvalidParams(err.Error())
	}

	// form model
	quota := model.NewQuotaFromRemoveRequest(req)

	err := s.uc.RemoveQuotaDir(ctx, quota)
	return &emptypb.Empty{}, err
}

func (s *MetaDataMiddlewareService) Mv(
	ctx context.Context, req *pb.MvRequest) (*emptypb.Empty, error) {

	if err := req.ValidateAll(); err != nil {
		return nil, errV1.ErrorInvalidParams(err.Error())
	}

	err := s.uc.Mv(ctx, req.Volume, req.Src, req.Dst)
	return &emptypb.Empty{}, err
}

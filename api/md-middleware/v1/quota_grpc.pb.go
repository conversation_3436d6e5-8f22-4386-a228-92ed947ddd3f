// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.12.4
// source: api/md-middleware/v1/quota.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	MetaDataMiddleware_SetQuota_FullMethodName       = "/api.md_middleware.v1.MetaDataMiddleware/SetQuota"
	MetaDataMiddleware_CreateQuotaDir_FullMethodName = "/api.md_middleware.v1.MetaDataMiddleware/CreateQuotaDir"
	MetaDataMiddleware_ListQuota_FullMethodName      = "/api.md_middleware.v1.MetaDataMiddleware/ListQuota"
	MetaDataMiddleware_UnsetQuota_FullMethodName     = "/api.md_middleware.v1.MetaDataMiddleware/UnsetQuota"
	MetaDataMiddleware_RemoveQuotaDir_FullMethodName = "/api.md_middleware.v1.MetaDataMiddleware/RemoveQuotaDir"
	MetaDataMiddleware_Mv_FullMethodName             = "/api.md_middleware.v1.MetaDataMiddleware/Mv"
)

// MetaDataMiddlewareClient is the client API for MetaDataMiddleware service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MetaDataMiddlewareClient interface {
	// set or update the quota only, dir exists already, idempotence
	SetQuota(ctx context.Context, in *SetQuotaRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// create dir and set the quota, idempotence
	CreateQuotaDir(ctx context.Context, in *SetQuotaRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// list quotas for the given paths, batch operation
	// list all quotas when the path list param is nil,
	// return only the usage if the path has no quota set, and the return size = -1
	ListQuota(ctx context.Context, in *ListQuotaRequest, opts ...grpc.CallOption) (*ListQuotaReply, error)
	// delete quota, dir remains
	UnsetQuota(ctx context.Context, in *UnsetQuotaRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// delete quota and the directory
	RemoveQuotaDir(ctx context.Context, in *RemoveQuotaRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	Mv(ctx context.Context, in *MvRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type metaDataMiddlewareClient struct {
	cc grpc.ClientConnInterface
}

func NewMetaDataMiddlewareClient(cc grpc.ClientConnInterface) MetaDataMiddlewareClient {
	return &metaDataMiddlewareClient{cc}
}

func (c *metaDataMiddlewareClient) SetQuota(ctx context.Context, in *SetQuotaRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, MetaDataMiddleware_SetQuota_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metaDataMiddlewareClient) CreateQuotaDir(ctx context.Context, in *SetQuotaRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, MetaDataMiddleware_CreateQuotaDir_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metaDataMiddlewareClient) ListQuota(ctx context.Context, in *ListQuotaRequest, opts ...grpc.CallOption) (*ListQuotaReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListQuotaReply)
	err := c.cc.Invoke(ctx, MetaDataMiddleware_ListQuota_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metaDataMiddlewareClient) UnsetQuota(ctx context.Context, in *UnsetQuotaRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, MetaDataMiddleware_UnsetQuota_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metaDataMiddlewareClient) RemoveQuotaDir(ctx context.Context, in *RemoveQuotaRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, MetaDataMiddleware_RemoveQuotaDir_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metaDataMiddlewareClient) Mv(ctx context.Context, in *MvRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, MetaDataMiddleware_Mv_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MetaDataMiddlewareServer is the server API for MetaDataMiddleware service.
// All implementations must embed UnimplementedMetaDataMiddlewareServer
// for forward compatibility.
type MetaDataMiddlewareServer interface {
	// set or update the quota only, dir exists already, idempotence
	SetQuota(context.Context, *SetQuotaRequest) (*emptypb.Empty, error)
	// create dir and set the quota, idempotence
	CreateQuotaDir(context.Context, *SetQuotaRequest) (*emptypb.Empty, error)
	// list quotas for the given paths, batch operation
	// list all quotas when the path list param is nil,
	// return only the usage if the path has no quota set, and the return size = -1
	ListQuota(context.Context, *ListQuotaRequest) (*ListQuotaReply, error)
	// delete quota, dir remains
	UnsetQuota(context.Context, *UnsetQuotaRequest) (*emptypb.Empty, error)
	// delete quota and the directory
	RemoveQuotaDir(context.Context, *RemoveQuotaRequest) (*emptypb.Empty, error)
	Mv(context.Context, *MvRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedMetaDataMiddlewareServer()
}

// UnimplementedMetaDataMiddlewareServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedMetaDataMiddlewareServer struct{}

func (UnimplementedMetaDataMiddlewareServer) SetQuota(context.Context, *SetQuotaRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetQuota not implemented")
}
func (UnimplementedMetaDataMiddlewareServer) CreateQuotaDir(context.Context, *SetQuotaRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateQuotaDir not implemented")
}
func (UnimplementedMetaDataMiddlewareServer) ListQuota(context.Context, *ListQuotaRequest) (*ListQuotaReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListQuota not implemented")
}
func (UnimplementedMetaDataMiddlewareServer) UnsetQuota(context.Context, *UnsetQuotaRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnsetQuota not implemented")
}
func (UnimplementedMetaDataMiddlewareServer) RemoveQuotaDir(context.Context, *RemoveQuotaRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveQuotaDir not implemented")
}
func (UnimplementedMetaDataMiddlewareServer) Mv(context.Context, *MvRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Mv not implemented")
}
func (UnimplementedMetaDataMiddlewareServer) mustEmbedUnimplementedMetaDataMiddlewareServer() {}
func (UnimplementedMetaDataMiddlewareServer) testEmbeddedByValue()                            {}

// UnsafeMetaDataMiddlewareServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MetaDataMiddlewareServer will
// result in compilation errors.
type UnsafeMetaDataMiddlewareServer interface {
	mustEmbedUnimplementedMetaDataMiddlewareServer()
}

func RegisterMetaDataMiddlewareServer(s grpc.ServiceRegistrar, srv MetaDataMiddlewareServer) {
	// If the following call pancis, it indicates UnimplementedMetaDataMiddlewareServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&MetaDataMiddleware_ServiceDesc, srv)
}

func _MetaDataMiddleware_SetQuota_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetQuotaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetaDataMiddlewareServer).SetQuota(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetaDataMiddleware_SetQuota_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetaDataMiddlewareServer).SetQuota(ctx, req.(*SetQuotaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetaDataMiddleware_CreateQuotaDir_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetQuotaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetaDataMiddlewareServer).CreateQuotaDir(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetaDataMiddleware_CreateQuotaDir_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetaDataMiddlewareServer).CreateQuotaDir(ctx, req.(*SetQuotaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetaDataMiddleware_ListQuota_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListQuotaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetaDataMiddlewareServer).ListQuota(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetaDataMiddleware_ListQuota_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetaDataMiddlewareServer).ListQuota(ctx, req.(*ListQuotaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetaDataMiddleware_UnsetQuota_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnsetQuotaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetaDataMiddlewareServer).UnsetQuota(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetaDataMiddleware_UnsetQuota_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetaDataMiddlewareServer).UnsetQuota(ctx, req.(*UnsetQuotaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetaDataMiddleware_RemoveQuotaDir_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveQuotaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetaDataMiddlewareServer).RemoveQuotaDir(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetaDataMiddleware_RemoveQuotaDir_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetaDataMiddlewareServer).RemoveQuotaDir(ctx, req.(*RemoveQuotaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetaDataMiddleware_Mv_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MvRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetaDataMiddlewareServer).Mv(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetaDataMiddleware_Mv_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetaDataMiddlewareServer).Mv(ctx, req.(*MvRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MetaDataMiddleware_ServiceDesc is the grpc.ServiceDesc for MetaDataMiddleware service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MetaDataMiddleware_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.md_middleware.v1.MetaDataMiddleware",
	HandlerType: (*MetaDataMiddlewareServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetQuota",
			Handler:    _MetaDataMiddleware_SetQuota_Handler,
		},
		{
			MethodName: "CreateQuotaDir",
			Handler:    _MetaDataMiddleware_CreateQuotaDir_Handler,
		},
		{
			MethodName: "ListQuota",
			Handler:    _MetaDataMiddleware_ListQuota_Handler,
		},
		{
			MethodName: "UnsetQuota",
			Handler:    _MetaDataMiddleware_UnsetQuota_Handler,
		},
		{
			MethodName: "RemoveQuotaDir",
			Handler:    _MetaDataMiddleware_RemoveQuotaDir_Handler,
		},
		{
			MethodName: "Mv",
			Handler:    _MetaDataMiddleware_Mv_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/md-middleware/v1/quota.proto",
}

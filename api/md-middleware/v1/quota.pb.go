// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.1
// 	protoc        v3.12.4
// source: api/md-middleware/v1/quota.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Quota_Type int32

const (
	Quota_TYPE_UNKNOWN Quota_Type = 0
	Quota_TYPE_USAGE   Quota_Type = 1 // capacity
	Quota_TYPE_OBJECTS Quota_Type = 2 // file numbers
)

// Enum value maps for Quota_Type.
var (
	Quota_Type_name = map[int32]string{
		0: "TYPE_UNKNOWN",
		1: "TYPE_USAGE",
		2: "TYPE_OBJECTS",
	}
	Quota_Type_value = map[string]int32{
		"TYPE_UNKNOWN": 0,
		"TYPE_USAGE":   1,
		"TYPE_OBJECTS": 2,
	}
)

func (x Quota_Type) Enum() *Quota_Type {
	p := new(Quota_Type)
	*p = x
	return p
}

func (x Quota_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Quota_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_api_md_middleware_v1_quota_proto_enumTypes[0].Descriptor()
}

func (Quota_Type) Type() protoreflect.EnumType {
	return &file_api_md_middleware_v1_quota_proto_enumTypes[0]
}

func (x Quota_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Quota_Type.Descriptor instead.
func (Quota_Type) EnumDescriptor() ([]byte, []int) {
	return file_api_md_middleware_v1_quota_proto_rawDescGZIP(), []int{0, 0}
}

type Quota struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Volume  string     `protobuf:"bytes,1,opt,name=volume,proto3" json:"volume,omitempty"`
	Type    Quota_Type `protobuf:"varint,2,opt,name=type,proto3,enum=api.md_middleware.v1.Quota_Type" json:"type,omitempty"`
	Path    string     `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`
	Size    int64      `protobuf:"zigzag64,4,opt,name=size,proto3" json:"size,omitempty"` // bytes, numbers
	Percent int32      `protobuf:"varint,5,opt,name=percent,proto3" json:"percent,omitempty"`
	Used    int64      `protobuf:"zigzag64,6,opt,name=used,proto3" json:"used,omitempty"` // bytes, numbers
}

func (x *Quota) Reset() {
	*x = Quota{}
	mi := &file_api_md_middleware_v1_quota_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Quota) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Quota) ProtoMessage() {}

func (x *Quota) ProtoReflect() protoreflect.Message {
	mi := &file_api_md_middleware_v1_quota_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Quota.ProtoReflect.Descriptor instead.
func (*Quota) Descriptor() ([]byte, []int) {
	return file_api_md_middleware_v1_quota_proto_rawDescGZIP(), []int{0}
}

func (x *Quota) GetVolume() string {
	if x != nil {
		return x.Volume
	}
	return ""
}

func (x *Quota) GetType() Quota_Type {
	if x != nil {
		return x.Type
	}
	return Quota_TYPE_UNKNOWN
}

func (x *Quota) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *Quota) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *Quota) GetPercent() int32 {
	if x != nil {
		return x.Percent
	}
	return 0
}

func (x *Quota) GetUsed() int64 {
	if x != nil {
		return x.Used
	}
	return 0
}

type SetQuotaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Volume  string     `protobuf:"bytes,1,opt,name=volume,proto3" json:"volume,omitempty"`                                   // e.g. devvolume, testvolume
	Type    Quota_Type `protobuf:"varint,2,opt,name=type,proto3,enum=api.md_middleware.v1.Quota_Type" json:"type,omitempty"` // e.g. 1, 2,
	Path    string     `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`                                       // e.g. /tenant-1/workspace-4
	Size    int64      `protobuf:"zigzag64,4,opt,name=size,proto3" json:"size,omitempty"`                                    // byte in numbers, -1 means no limit, 0 means 0 byte
	Percent int32      `protobuf:"varint,5,opt,name=percent,proto3" json:"percent,omitempty"`                                // e.g. default 80
}

func (x *SetQuotaRequest) Reset() {
	*x = SetQuotaRequest{}
	mi := &file_api_md_middleware_v1_quota_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetQuotaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetQuotaRequest) ProtoMessage() {}

func (x *SetQuotaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_md_middleware_v1_quota_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetQuotaRequest.ProtoReflect.Descriptor instead.
func (*SetQuotaRequest) Descriptor() ([]byte, []int) {
	return file_api_md_middleware_v1_quota_proto_rawDescGZIP(), []int{1}
}

func (x *SetQuotaRequest) GetVolume() string {
	if x != nil {
		return x.Volume
	}
	return ""
}

func (x *SetQuotaRequest) GetType() Quota_Type {
	if x != nil {
		return x.Type
	}
	return Quota_TYPE_UNKNOWN
}

func (x *SetQuotaRequest) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *SetQuotaRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *SetQuotaRequest) GetPercent() int32 {
	if x != nil {
		return x.Percent
	}
	return 0
}

type ListQuotaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Volume string     `protobuf:"bytes,1,opt,name=volume,proto3" json:"volume,omitempty"`
	Type   Quota_Type `protobuf:"varint,2,opt,name=type,proto3,enum=api.md_middleware.v1.Quota_Type" json:"type,omitempty"`
	Paths  []string   `protobuf:"bytes,3,rep,name=paths,proto3" json:"paths,omitempty"` // e.g. ["/tenant-1/workspace-4", "/tenant-1/workspace-2"]
}

func (x *ListQuotaRequest) Reset() {
	*x = ListQuotaRequest{}
	mi := &file_api_md_middleware_v1_quota_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListQuotaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListQuotaRequest) ProtoMessage() {}

func (x *ListQuotaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_md_middleware_v1_quota_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListQuotaRequest.ProtoReflect.Descriptor instead.
func (*ListQuotaRequest) Descriptor() ([]byte, []int) {
	return file_api_md_middleware_v1_quota_proto_rawDescGZIP(), []int{2}
}

func (x *ListQuotaRequest) GetVolume() string {
	if x != nil {
		return x.Volume
	}
	return ""
}

func (x *ListQuotaRequest) GetType() Quota_Type {
	if x != nil {
		return x.Type
	}
	return Quota_TYPE_UNKNOWN
}

func (x *ListQuotaRequest) GetPaths() []string {
	if x != nil {
		return x.Paths
	}
	return nil
}

type ListQuotaReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Quotas []*Quota `protobuf:"bytes,3,rep,name=quotas,proto3" json:"quotas,omitempty"`
}

func (x *ListQuotaReply) Reset() {
	*x = ListQuotaReply{}
	mi := &file_api_md_middleware_v1_quota_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListQuotaReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListQuotaReply) ProtoMessage() {}

func (x *ListQuotaReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_md_middleware_v1_quota_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListQuotaReply.ProtoReflect.Descriptor instead.
func (*ListQuotaReply) Descriptor() ([]byte, []int) {
	return file_api_md_middleware_v1_quota_proto_rawDescGZIP(), []int{3}
}

func (x *ListQuotaReply) GetQuotas() []*Quota {
	if x != nil {
		return x.Quotas
	}
	return nil
}

type UnsetQuotaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Volume string     `protobuf:"bytes,1,opt,name=volume,proto3" json:"volume,omitempty"`                                   // e.g. devvolume, testvolume
	Type   Quota_Type `protobuf:"varint,2,opt,name=type,proto3,enum=api.md_middleware.v1.Quota_Type" json:"type,omitempty"` // e.g. 1, 2,
	Path   string     `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`                                       // e.g. /tenant-1/workspace-4
}

func (x *UnsetQuotaRequest) Reset() {
	*x = UnsetQuotaRequest{}
	mi := &file_api_md_middleware_v1_quota_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnsetQuotaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnsetQuotaRequest) ProtoMessage() {}

func (x *UnsetQuotaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_md_middleware_v1_quota_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnsetQuotaRequest.ProtoReflect.Descriptor instead.
func (*UnsetQuotaRequest) Descriptor() ([]byte, []int) {
	return file_api_md_middleware_v1_quota_proto_rawDescGZIP(), []int{4}
}

func (x *UnsetQuotaRequest) GetVolume() string {
	if x != nil {
		return x.Volume
	}
	return ""
}

func (x *UnsetQuotaRequest) GetType() Quota_Type {
	if x != nil {
		return x.Type
	}
	return Quota_TYPE_UNKNOWN
}

func (x *UnsetQuotaRequest) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

type RemoveQuotaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Volume string `protobuf:"bytes,1,opt,name=volume,proto3" json:"volume,omitempty"` // e.g. devvolume, testvolume
	Path   string `protobuf:"bytes,2,opt,name=path,proto3" json:"path,omitempty"`     // e.g. /tenant-1/workspace-4
}

func (x *RemoveQuotaRequest) Reset() {
	*x = RemoveQuotaRequest{}
	mi := &file_api_md_middleware_v1_quota_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveQuotaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveQuotaRequest) ProtoMessage() {}

func (x *RemoveQuotaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_md_middleware_v1_quota_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveQuotaRequest.ProtoReflect.Descriptor instead.
func (*RemoveQuotaRequest) Descriptor() ([]byte, []int) {
	return file_api_md_middleware_v1_quota_proto_rawDescGZIP(), []int{5}
}

func (x *RemoveQuotaRequest) GetVolume() string {
	if x != nil {
		return x.Volume
	}
	return ""
}

func (x *RemoveQuotaRequest) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

type MvRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Volume string `protobuf:"bytes,1,opt,name=volume,proto3" json:"volume,omitempty"` // e.g. devvolume, testvolume
	Src    string `protobuf:"bytes,2,opt,name=src,proto3" json:"src,omitempty"`       // e.g. /tenant-1/workspace-4
	Dst    string `protobuf:"bytes,3,opt,name=dst,proto3" json:"dst,omitempty"`       // e.g. /tenant-1/workspace-4
}

func (x *MvRequest) Reset() {
	*x = MvRequest{}
	mi := &file_api_md_middleware_v1_quota_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MvRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MvRequest) ProtoMessage() {}

func (x *MvRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_md_middleware_v1_quota_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MvRequest.ProtoReflect.Descriptor instead.
func (*MvRequest) Descriptor() ([]byte, []int) {
	return file_api_md_middleware_v1_quota_proto_rawDescGZIP(), []int{6}
}

func (x *MvRequest) GetVolume() string {
	if x != nil {
		return x.Volume
	}
	return ""
}

func (x *MvRequest) GetSrc() string {
	if x != nil {
		return x.Src
	}
	return ""
}

func (x *MvRequest) GetDst() string {
	if x != nil {
		return x.Dst
	}
	return ""
}

var File_api_md_middleware_v1_quota_proto protoreflect.FileDescriptor

var file_api_md_middleware_v1_quota_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x64, 0x2d, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x77,
	0x61, 0x72, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x14, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x64, 0x5f, 0x6d, 0x69, 0x64, 0x64, 0x6c,
	0x65, 0x77, 0x61, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe7, 0x01, 0x0a,
	0x05, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x34,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6d, 0x64, 0x5f, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x77, 0x61, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x12, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70,
	0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x12, 0x52, 0x04, 0x75, 0x73, 0x65, 0x64, 0x22, 0x3a, 0x0a, 0x04, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x53, 0x41,
	0x47, 0x45, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x42, 0x4a,
	0x45, 0x43, 0x54, 0x53, 0x10, 0x02, 0x22, 0xd6, 0x01, 0x0a, 0x0f, 0x53, 0x65, 0x74, 0x51, 0x75,
	0x6f, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x06, 0x76, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x3e, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6d, 0x64, 0x5f, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x77, 0x61, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x04, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x72, 0x07,
	0x32, 0x05, 0x5e, 0x2f, 0x2e, 0x2a, 0x24, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x1b, 0x0a,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x12, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x42, 0x02, 0x28, 0x01, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x23, 0x0a, 0x07, 0x70, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x1a, 0x04, 0x18, 0x64, 0x28, 0x00, 0x52, 0x07, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x22,
	0x89, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x76,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x3e, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x64, 0x5f, 0x6d, 0x69, 0x64,
	0x64, 0x6c, 0x65, 0x77, 0x61, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x6f, 0x74, 0x61,
	0x2e, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x61, 0x74, 0x68, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x70, 0x61, 0x74, 0x68, 0x73, 0x22, 0x45, 0x0a, 0x0e, 0x4c,
	0x69, 0x73, 0x74, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x33, 0x0a,
	0x06, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6d, 0x64, 0x5f, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x77, 0x61, 0x72,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x52, 0x06, 0x71, 0x75, 0x6f, 0x74,
	0x61, 0x73, 0x22, 0x96, 0x01, 0x0a, 0x11, 0x55, 0x6e, 0x73, 0x65, 0x74, 0x51, 0x75, 0x6f, 0x74,
	0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x06, 0x76, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x3e, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x64,
	0x5f, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x77, 0x61, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x51,
	0x75, 0x6f, 0x74, 0x61, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01,
	0x02, 0x20, 0x00, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x04, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x72, 0x07, 0x32, 0x05,
	0x5e, 0x2f, 0x2e, 0x2a, 0x24, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x22, 0x57, 0x0a, 0x12, 0x52,
	0x65, 0x6d, 0x6f, 0x76, 0x65, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1f, 0x0a, 0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x76, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x12, 0x20, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0c, 0xfa, 0x42, 0x09, 0x72, 0x07, 0x32, 0x05, 0x5e, 0x2f, 0x2e, 0x2a, 0x24, 0x52, 0x04,
	0x70, 0x61, 0x74, 0x68, 0x22, 0x6c, 0x0a, 0x09, 0x4d, 0x76, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1f, 0x0a, 0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x76, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x03, 0x73, 0x72, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0c, 0xfa, 0x42, 0x09, 0x72, 0x07, 0x32, 0x05, 0x5e, 0x2f, 0x2e, 0x2a, 0x24, 0x52, 0x03, 0x73,
	0x72, 0x63, 0x12, 0x1e, 0x0a, 0x03, 0x64, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0c, 0xfa, 0x42, 0x09, 0x72, 0x07, 0x32, 0x05, 0x5e, 0x2f, 0x2e, 0x2a, 0x24, 0x52, 0x03, 0x64,
	0x73, 0x74, 0x32, 0xf3, 0x04, 0x0a, 0x12, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x4d,
	0x69, 0x64, 0x64, 0x6c, 0x65, 0x77, 0x61, 0x72, 0x65, 0x12, 0x60, 0x0a, 0x08, 0x53, 0x65, 0x74,
	0x51, 0x75, 0x6f, 0x74, 0x61, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x64, 0x5f, 0x6d,
	0x69, 0x64, 0x64, 0x6c, 0x65, 0x77, 0x61, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74,
	0x51, 0x75, 0x6f, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x15, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0f, 0x3a, 0x01, 0x2a, 0x1a,
	0x0a, 0x2f, 0x76, 0x31, 0x2f, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x73, 0x12, 0x66, 0x0a, 0x0e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x44, 0x69, 0x72, 0x12, 0x25, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6d, 0x64, 0x5f, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x77, 0x61, 0x72,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x15, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x0f, 0x3a, 0x01, 0x2a, 0x22, 0x0a, 0x2f, 0x76, 0x31, 0x2f, 0x71, 0x75, 0x6f,
	0x74, 0x61, 0x73, 0x12, 0x6d, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x51, 0x75, 0x6f, 0x74, 0x61,
	0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x64, 0x5f, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65,
	0x77, 0x61, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x51, 0x75, 0x6f, 0x74,
	0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d,
	0x64, 0x5f, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x77, 0x61, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x12,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0c, 0x12, 0x0a, 0x2f, 0x76, 0x31, 0x2f, 0x71, 0x75, 0x6f, 0x74,
	0x61, 0x73, 0x12, 0x64, 0x0a, 0x0a, 0x55, 0x6e, 0x73, 0x65, 0x74, 0x51, 0x75, 0x6f, 0x74, 0x61,
	0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x64, 0x5f, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65,
	0x77, 0x61, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x6e, 0x73, 0x65, 0x74, 0x51, 0x75, 0x6f,
	0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x15, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0f, 0x3a, 0x01, 0x2a, 0x32, 0x0a, 0x2f, 0x76,
	0x31, 0x2f, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x73, 0x12, 0x66, 0x0a, 0x0e, 0x52, 0x65, 0x6d, 0x6f,
	0x76, 0x65, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x44, 0x69, 0x72, 0x12, 0x28, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6d, 0x64, 0x5f, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x77, 0x61, 0x72, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x12, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x0c, 0x2a, 0x0a, 0x2f, 0x76, 0x31, 0x2f, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x73,
	0x12, 0x56, 0x0a, 0x02, 0x4d, 0x76, 0x12, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x64, 0x5f,
	0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x77, 0x61, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x76,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x17, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x11, 0x3a, 0x01, 0x2a, 0x22, 0x0c, 0x2f, 0x76, 0x31, 0x2f,
	0x70, 0x6f, 0x73, 0x69, 0x78, 0x2f, 0x6d, 0x76, 0x42, 0x82, 0x01, 0x0a, 0x1a, 0x61, 0x70, 0x69,
	0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2d, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65,
	0x77, 0x61, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x42, 0x12, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74,
	0x61, 0x4d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x77, 0x61, 0x72, 0x65, 0x50, 0x01, 0x5a, 0x4e, 0x67,
	0x69, 0x74, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x2e, 0x74, 0x65, 0x63, 0x6f, 0x72, 0x69, 0x67, 0x69,
	0x6e, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x61, 0x69, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x2f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2d, 0x6d, 0x69, 0x64, 0x64, 0x6c,
	0x65, 0x77, 0x61, 0x72, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x64, 0x2d, 0x6d, 0x69, 0x64,
	0x64, 0x6c, 0x65, 0x77, 0x61, 0x72, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_md_middleware_v1_quota_proto_rawDescOnce sync.Once
	file_api_md_middleware_v1_quota_proto_rawDescData = file_api_md_middleware_v1_quota_proto_rawDesc
)

func file_api_md_middleware_v1_quota_proto_rawDescGZIP() []byte {
	file_api_md_middleware_v1_quota_proto_rawDescOnce.Do(func() {
		file_api_md_middleware_v1_quota_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_md_middleware_v1_quota_proto_rawDescData)
	})
	return file_api_md_middleware_v1_quota_proto_rawDescData
}

var file_api_md_middleware_v1_quota_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_md_middleware_v1_quota_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_api_md_middleware_v1_quota_proto_goTypes = []any{
	(Quota_Type)(0),            // 0: api.md_middleware.v1.Quota.Type
	(*Quota)(nil),              // 1: api.md_middleware.v1.Quota
	(*SetQuotaRequest)(nil),    // 2: api.md_middleware.v1.SetQuotaRequest
	(*ListQuotaRequest)(nil),   // 3: api.md_middleware.v1.ListQuotaRequest
	(*ListQuotaReply)(nil),     // 4: api.md_middleware.v1.ListQuotaReply
	(*UnsetQuotaRequest)(nil),  // 5: api.md_middleware.v1.UnsetQuotaRequest
	(*RemoveQuotaRequest)(nil), // 6: api.md_middleware.v1.RemoveQuotaRequest
	(*MvRequest)(nil),          // 7: api.md_middleware.v1.MvRequest
	(*emptypb.Empty)(nil),      // 8: google.protobuf.Empty
}
var file_api_md_middleware_v1_quota_proto_depIdxs = []int32{
	0,  // 0: api.md_middleware.v1.Quota.type:type_name -> api.md_middleware.v1.Quota.Type
	0,  // 1: api.md_middleware.v1.SetQuotaRequest.type:type_name -> api.md_middleware.v1.Quota.Type
	0,  // 2: api.md_middleware.v1.ListQuotaRequest.type:type_name -> api.md_middleware.v1.Quota.Type
	1,  // 3: api.md_middleware.v1.ListQuotaReply.quotas:type_name -> api.md_middleware.v1.Quota
	0,  // 4: api.md_middleware.v1.UnsetQuotaRequest.type:type_name -> api.md_middleware.v1.Quota.Type
	2,  // 5: api.md_middleware.v1.MetaDataMiddleware.SetQuota:input_type -> api.md_middleware.v1.SetQuotaRequest
	2,  // 6: api.md_middleware.v1.MetaDataMiddleware.CreateQuotaDir:input_type -> api.md_middleware.v1.SetQuotaRequest
	3,  // 7: api.md_middleware.v1.MetaDataMiddleware.ListQuota:input_type -> api.md_middleware.v1.ListQuotaRequest
	5,  // 8: api.md_middleware.v1.MetaDataMiddleware.UnsetQuota:input_type -> api.md_middleware.v1.UnsetQuotaRequest
	6,  // 9: api.md_middleware.v1.MetaDataMiddleware.RemoveQuotaDir:input_type -> api.md_middleware.v1.RemoveQuotaRequest
	7,  // 10: api.md_middleware.v1.MetaDataMiddleware.Mv:input_type -> api.md_middleware.v1.MvRequest
	8,  // 11: api.md_middleware.v1.MetaDataMiddleware.SetQuota:output_type -> google.protobuf.Empty
	8,  // 12: api.md_middleware.v1.MetaDataMiddleware.CreateQuotaDir:output_type -> google.protobuf.Empty
	4,  // 13: api.md_middleware.v1.MetaDataMiddleware.ListQuota:output_type -> api.md_middleware.v1.ListQuotaReply
	8,  // 14: api.md_middleware.v1.MetaDataMiddleware.UnsetQuota:output_type -> google.protobuf.Empty
	8,  // 15: api.md_middleware.v1.MetaDataMiddleware.RemoveQuotaDir:output_type -> google.protobuf.Empty
	8,  // 16: api.md_middleware.v1.MetaDataMiddleware.Mv:output_type -> google.protobuf.Empty
	11, // [11:17] is the sub-list for method output_type
	5,  // [5:11] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_api_md_middleware_v1_quota_proto_init() }
func file_api_md_middleware_v1_quota_proto_init() {
	if File_api_md_middleware_v1_quota_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_md_middleware_v1_quota_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_md_middleware_v1_quota_proto_goTypes,
		DependencyIndexes: file_api_md_middleware_v1_quota_proto_depIdxs,
		EnumInfos:         file_api_md_middleware_v1_quota_proto_enumTypes,
		MessageInfos:      file_api_md_middleware_v1_quota_proto_msgTypes,
	}.Build()
	File_api_md_middleware_v1_quota_proto = out.File
	file_api_md_middleware_v1_quota_proto_rawDesc = nil
	file_api_md_middleware_v1_quota_proto_goTypes = nil
	file_api_md_middleware_v1_quota_proto_depIdxs = nil
}

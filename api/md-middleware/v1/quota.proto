syntax = "proto3";

package api.md_middleware.v1;

import "google/protobuf/empty.proto";
import "google/api/annotations.proto";
import "validate/validate.proto";

option go_package = "git-plat.tecorigin.net/ai-platform/metadata-middleware/api/md-middleware/v1;v1";
option java_multiple_files = true;
option java_package = "api.metadata-middleware.v1";
option java_outer_classname = "MetaDataMiddleware";
 
service MetaDataMiddleware {
	// set or update the quota only, dir exists already, idempotence
  rpc SetQuota (SetQuotaRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/v1/quotas",
      body: "*"
    };
  }

  // create dir and set the quota, idempotence
  rpc CreateQuotaDir (SetQuotaRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/quotas",
      body: "*"
    };
  }

  // list quotas for the given paths, batch operation
  // list all quotas when the path list param is nil,
  // return only the usage if the path has no quota set, and the return size = -1
  rpc ListQuota (ListQuotaRequest) returns (ListQuotaReply) {
    option (google.api.http) = {
      get: "/v1/quotas",
    };
  }

  // delete quota, dir remains
  rpc UnsetQuota (UnsetQuotaRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      patch: "/v1/quotas",
      body: "*"
    };
  }

  // delete quota and the directory
  rpc RemoveQuotaDir (RemoveQuotaRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/quotas",
    };
  }

  rpc Mv (MvRequest) returns (google.protobuf.Empty){
    option (google.api.http) = {
      post: "/v1/posix/mv",
      body: "*"
    }; 
  }
}

message Quota {
  enum Type {
    TYPE_UNKNOWN = 0;
    TYPE_USAGE = 1;      // capacity
    TYPE_OBJECTS = 2;    // file numbers
  }
  string volume = 1;
  Type type = 2;
  string path = 3;
  sint64 size = 4;   // bytes, numbers
  int32 percent = 5;
  sint64 used = 6;   // bytes, numbers
}

message SetQuotaRequest {
  string volume = 1 [(validate.rules).string.min_len = 1];         // e.g. devvolume, testvolume
  Quota.Type type = 2 [(validate.rules).enum = {not_in: [0]}];     // e.g. 1, 2, 
  string path = 3  [(validate.rules).string.pattern = "^/.*$"];    // e.g. /tenant-1/workspace-4
  sint64 size = 4 [(validate.rules).sint64 = {gte:-1}];            // byte in numbers, -1 means no limit, 0 means 0 byte
  int32 percent = 5 [(validate.rules).int32 = {gte:0, lte: 100}];  // e.g. default 80
}

message ListQuotaRequest {
  string volume = 1 [(validate.rules).string.min_len = 1]; 
  Quota.Type type = 2 [(validate.rules).enum = {not_in: [0]}];
  repeated string paths = 3;    // e.g. ["/tenant-1/workspace-4", "/tenant-1/workspace-2"]
}

message ListQuotaReply {
  repeated Quota quotas = 3;
}

message UnsetQuotaRequest {
  string volume = 1   [(validate.rules).string.min_len = 1];          // e.g. devvolume, testvolume
  Quota.Type type = 2 [(validate.rules).enum = {not_in: [0]}];        // e.g. 1, 2, 
  string path = 3    [(validate.rules).string.pattern = "^/.*$"];     // e.g. /tenant-1/workspace-4
}

message RemoveQuotaRequest {
  string volume = 1   [(validate.rules).string.min_len = 1];          // e.g. devvolume, testvolume
  string path = 2    [(validate.rules).string.pattern = "^/.*$"];     // e.g. /tenant-1/workspace-4
}

message MvRequest {
  string volume = 1  [(validate.rules).string.min_len = 1];         // e.g. devvolume, testvolume
  string src = 2  [(validate.rules).string.pattern = "^/.*$"];     // e.g. /tenant-1/workspace-4
  string dst = 3  [(validate.rules).string.pattern = "^/.*$"];     // e.g. /tenant-1/workspace-4
}

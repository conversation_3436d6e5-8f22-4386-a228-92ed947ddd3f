// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.2
// - protoc             v3.12.4
// source: api/md-middleware/v1/quota.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationMetaDataMiddlewareCreateQuotaDir = "/api.md_middleware.v1.MetaDataMiddleware/CreateQuotaDir"
const OperationMetaDataMiddlewareListQuota = "/api.md_middleware.v1.MetaDataMiddleware/ListQuota"
const OperationMetaDataMiddlewareMv = "/api.md_middleware.v1.MetaDataMiddleware/Mv"
const OperationMetaDataMiddlewareRemoveQuotaDir = "/api.md_middleware.v1.MetaDataMiddleware/RemoveQuotaDir"
const OperationMetaDataMiddlewareSetQuota = "/api.md_middleware.v1.MetaDataMiddleware/SetQuota"
const OperationMetaDataMiddlewareUnsetQuota = "/api.md_middleware.v1.MetaDataMiddleware/UnsetQuota"

type MetaDataMiddlewareHTTPServer interface {
	// CreateQuotaDir create dir and set the quota, idempotence
	CreateQuotaDir(context.Context, *SetQuotaRequest) (*emptypb.Empty, error)
	// ListQuota list quotas for the given paths, batch operation
	// list all quotas when the path list param is nil,
	// return only the usage if the path has no quota set, and the return size = -1
	ListQuota(context.Context, *ListQuotaRequest) (*ListQuotaReply, error)
	Mv(context.Context, *MvRequest) (*emptypb.Empty, error)
	// RemoveQuotaDir delete quota and the directory
	RemoveQuotaDir(context.Context, *RemoveQuotaRequest) (*emptypb.Empty, error)
	// SetQuota set or update the quota only, dir exists already, idempotence
	SetQuota(context.Context, *SetQuotaRequest) (*emptypb.Empty, error)
	// UnsetQuota delete quota, dir remains
	UnsetQuota(context.Context, *UnsetQuotaRequest) (*emptypb.Empty, error)
}

func RegisterMetaDataMiddlewareHTTPServer(s *http.Server, srv MetaDataMiddlewareHTTPServer) {
	r := s.Route("/")
	r.PUT("/v1/quotas", _MetaDataMiddleware_SetQuota0_HTTP_Handler(srv))
	r.POST("/v1/quotas", _MetaDataMiddleware_CreateQuotaDir0_HTTP_Handler(srv))
	r.GET("/v1/quotas", _MetaDataMiddleware_ListQuota0_HTTP_Handler(srv))
	r.PATCH("/v1/quotas", _MetaDataMiddleware_UnsetQuota0_HTTP_Handler(srv))
	r.DELETE("/v1/quotas", _MetaDataMiddleware_RemoveQuotaDir0_HTTP_Handler(srv))
	r.POST("/v1/posix/mv", _MetaDataMiddleware_Mv0_HTTP_Handler(srv))
}

func _MetaDataMiddleware_SetQuota0_HTTP_Handler(srv MetaDataMiddlewareHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetQuotaRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMetaDataMiddlewareSetQuota)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetQuota(ctx, req.(*SetQuotaRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _MetaDataMiddleware_CreateQuotaDir0_HTTP_Handler(srv MetaDataMiddlewareHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetQuotaRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMetaDataMiddlewareCreateQuotaDir)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateQuotaDir(ctx, req.(*SetQuotaRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _MetaDataMiddleware_ListQuota0_HTTP_Handler(srv MetaDataMiddlewareHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListQuotaRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMetaDataMiddlewareListQuota)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListQuota(ctx, req.(*ListQuotaRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListQuotaReply)
		return ctx.Result(200, reply)
	}
}

func _MetaDataMiddleware_UnsetQuota0_HTTP_Handler(srv MetaDataMiddlewareHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UnsetQuotaRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMetaDataMiddlewareUnsetQuota)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UnsetQuota(ctx, req.(*UnsetQuotaRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _MetaDataMiddleware_RemoveQuotaDir0_HTTP_Handler(srv MetaDataMiddlewareHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RemoveQuotaRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMetaDataMiddlewareRemoveQuotaDir)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RemoveQuotaDir(ctx, req.(*RemoveQuotaRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _MetaDataMiddleware_Mv0_HTTP_Handler(srv MetaDataMiddlewareHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in MvRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMetaDataMiddlewareMv)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Mv(ctx, req.(*MvRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type MetaDataMiddlewareHTTPClient interface {
	CreateQuotaDir(ctx context.Context, req *SetQuotaRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	ListQuota(ctx context.Context, req *ListQuotaRequest, opts ...http.CallOption) (rsp *ListQuotaReply, err error)
	Mv(ctx context.Context, req *MvRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	RemoveQuotaDir(ctx context.Context, req *RemoveQuotaRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	SetQuota(ctx context.Context, req *SetQuotaRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	UnsetQuota(ctx context.Context, req *UnsetQuotaRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type MetaDataMiddlewareHTTPClientImpl struct {
	cc *http.Client
}

func NewMetaDataMiddlewareHTTPClient(client *http.Client) MetaDataMiddlewareHTTPClient {
	return &MetaDataMiddlewareHTTPClientImpl{client}
}

func (c *MetaDataMiddlewareHTTPClientImpl) CreateQuotaDir(ctx context.Context, in *SetQuotaRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/quotas"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationMetaDataMiddlewareCreateQuotaDir))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *MetaDataMiddlewareHTTPClientImpl) ListQuota(ctx context.Context, in *ListQuotaRequest, opts ...http.CallOption) (*ListQuotaReply, error) {
	var out ListQuotaReply
	pattern := "/v1/quotas"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationMetaDataMiddlewareListQuota))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *MetaDataMiddlewareHTTPClientImpl) Mv(ctx context.Context, in *MvRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/posix/mv"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationMetaDataMiddlewareMv))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *MetaDataMiddlewareHTTPClientImpl) RemoveQuotaDir(ctx context.Context, in *RemoveQuotaRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/quotas"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationMetaDataMiddlewareRemoveQuotaDir))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *MetaDataMiddlewareHTTPClientImpl) SetQuota(ctx context.Context, in *SetQuotaRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/quotas"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationMetaDataMiddlewareSetQuota))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *MetaDataMiddlewareHTTPClientImpl) UnsetQuota(ctx context.Context, in *UnsetQuotaRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/quotas"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationMetaDataMiddlewareUnsetQuota))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PATCH", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/md-middleware/v1/quota.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Quota with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Quota) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Quota with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in QuotaMultiError, or nil if none found.
func (m *Quota) ValidateAll() error {
	return m.validate(true)
}

func (m *Quota) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Volume

	// no validation rules for Type

	// no validation rules for Path

	// no validation rules for Size

	// no validation rules for Percent

	// no validation rules for Used

	if len(errors) > 0 {
		return QuotaMultiError(errors)
	}

	return nil
}

// QuotaMultiError is an error wrapping multiple validation errors returned by
// Quota.ValidateAll() if the designated constraints aren't met.
type QuotaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuotaMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuotaMultiError) AllErrors() []error { return m }

// QuotaValidationError is the validation error returned by Quota.Validate if
// the designated constraints aren't met.
type QuotaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuotaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuotaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuotaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuotaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuotaValidationError) ErrorName() string { return "QuotaValidationError" }

// Error satisfies the builtin error interface
func (e QuotaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuota.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuotaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuotaValidationError{}

// Validate checks the field values on SetQuotaRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SetQuotaRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetQuotaRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetQuotaRequestMultiError, or nil if none found.
func (m *SetQuotaRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SetQuotaRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetVolume()) < 1 {
		err := SetQuotaRequestValidationError{
			field:  "Volume",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _SetQuotaRequest_Type_NotInLookup[m.GetType()]; ok {
		err := SetQuotaRequestValidationError{
			field:  "Type",
			reason: "value must not be in list [TYPE_UNKNOWN]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_SetQuotaRequest_Path_Pattern.MatchString(m.GetPath()) {
		err := SetQuotaRequestValidationError{
			field:  "Path",
			reason: "value does not match regex pattern \"^/.*$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetSize() < -1 {
		err := SetQuotaRequestValidationError{
			field:  "Size",
			reason: "value must be greater than or equal to -1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetPercent(); val < 0 || val > 100 {
		err := SetQuotaRequestValidationError{
			field:  "Percent",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SetQuotaRequestMultiError(errors)
	}

	return nil
}

// SetQuotaRequestMultiError is an error wrapping multiple validation errors
// returned by SetQuotaRequest.ValidateAll() if the designated constraints
// aren't met.
type SetQuotaRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetQuotaRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetQuotaRequestMultiError) AllErrors() []error { return m }

// SetQuotaRequestValidationError is the validation error returned by
// SetQuotaRequest.Validate if the designated constraints aren't met.
type SetQuotaRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetQuotaRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetQuotaRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetQuotaRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetQuotaRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetQuotaRequestValidationError) ErrorName() string { return "SetQuotaRequestValidationError" }

// Error satisfies the builtin error interface
func (e SetQuotaRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetQuotaRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetQuotaRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetQuotaRequestValidationError{}

var _SetQuotaRequest_Type_NotInLookup = map[Quota_Type]struct{}{
	0: {},
}

var _SetQuotaRequest_Path_Pattern = regexp.MustCompile("^/.*$")

// Validate checks the field values on ListQuotaRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListQuotaRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListQuotaRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListQuotaRequestMultiError, or nil if none found.
func (m *ListQuotaRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListQuotaRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetVolume()) < 1 {
		err := ListQuotaRequestValidationError{
			field:  "Volume",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _ListQuotaRequest_Type_NotInLookup[m.GetType()]; ok {
		err := ListQuotaRequestValidationError{
			field:  "Type",
			reason: "value must not be in list [TYPE_UNKNOWN]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ListQuotaRequestMultiError(errors)
	}

	return nil
}

// ListQuotaRequestMultiError is an error wrapping multiple validation errors
// returned by ListQuotaRequest.ValidateAll() if the designated constraints
// aren't met.
type ListQuotaRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListQuotaRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListQuotaRequestMultiError) AllErrors() []error { return m }

// ListQuotaRequestValidationError is the validation error returned by
// ListQuotaRequest.Validate if the designated constraints aren't met.
type ListQuotaRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListQuotaRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListQuotaRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListQuotaRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListQuotaRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListQuotaRequestValidationError) ErrorName() string { return "ListQuotaRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListQuotaRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListQuotaRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListQuotaRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListQuotaRequestValidationError{}

var _ListQuotaRequest_Type_NotInLookup = map[Quota_Type]struct{}{
	0: {},
}

// Validate checks the field values on ListQuotaReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListQuotaReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListQuotaReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListQuotaReplyMultiError,
// or nil if none found.
func (m *ListQuotaReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListQuotaReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetQuotas() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListQuotaReplyValidationError{
						field:  fmt.Sprintf("Quotas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListQuotaReplyValidationError{
						field:  fmt.Sprintf("Quotas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListQuotaReplyValidationError{
					field:  fmt.Sprintf("Quotas[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListQuotaReplyMultiError(errors)
	}

	return nil
}

// ListQuotaReplyMultiError is an error wrapping multiple validation errors
// returned by ListQuotaReply.ValidateAll() if the designated constraints
// aren't met.
type ListQuotaReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListQuotaReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListQuotaReplyMultiError) AllErrors() []error { return m }

// ListQuotaReplyValidationError is the validation error returned by
// ListQuotaReply.Validate if the designated constraints aren't met.
type ListQuotaReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListQuotaReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListQuotaReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListQuotaReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListQuotaReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListQuotaReplyValidationError) ErrorName() string { return "ListQuotaReplyValidationError" }

// Error satisfies the builtin error interface
func (e ListQuotaReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListQuotaReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListQuotaReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListQuotaReplyValidationError{}

// Validate checks the field values on UnsetQuotaRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UnsetQuotaRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnsetQuotaRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnsetQuotaRequestMultiError, or nil if none found.
func (m *UnsetQuotaRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UnsetQuotaRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetVolume()) < 1 {
		err := UnsetQuotaRequestValidationError{
			field:  "Volume",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _UnsetQuotaRequest_Type_NotInLookup[m.GetType()]; ok {
		err := UnsetQuotaRequestValidationError{
			field:  "Type",
			reason: "value must not be in list [TYPE_UNKNOWN]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_UnsetQuotaRequest_Path_Pattern.MatchString(m.GetPath()) {
		err := UnsetQuotaRequestValidationError{
			field:  "Path",
			reason: "value does not match regex pattern \"^/.*$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UnsetQuotaRequestMultiError(errors)
	}

	return nil
}

// UnsetQuotaRequestMultiError is an error wrapping multiple validation errors
// returned by UnsetQuotaRequest.ValidateAll() if the designated constraints
// aren't met.
type UnsetQuotaRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnsetQuotaRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnsetQuotaRequestMultiError) AllErrors() []error { return m }

// UnsetQuotaRequestValidationError is the validation error returned by
// UnsetQuotaRequest.Validate if the designated constraints aren't met.
type UnsetQuotaRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnsetQuotaRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnsetQuotaRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnsetQuotaRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnsetQuotaRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnsetQuotaRequestValidationError) ErrorName() string {
	return "UnsetQuotaRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UnsetQuotaRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnsetQuotaRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnsetQuotaRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnsetQuotaRequestValidationError{}

var _UnsetQuotaRequest_Type_NotInLookup = map[Quota_Type]struct{}{
	0: {},
}

var _UnsetQuotaRequest_Path_Pattern = regexp.MustCompile("^/.*$")

// Validate checks the field values on RemoveQuotaRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RemoveQuotaRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RemoveQuotaRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RemoveQuotaRequestMultiError, or nil if none found.
func (m *RemoveQuotaRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RemoveQuotaRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetVolume()) < 1 {
		err := RemoveQuotaRequestValidationError{
			field:  "Volume",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_RemoveQuotaRequest_Path_Pattern.MatchString(m.GetPath()) {
		err := RemoveQuotaRequestValidationError{
			field:  "Path",
			reason: "value does not match regex pattern \"^/.*$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return RemoveQuotaRequestMultiError(errors)
	}

	return nil
}

// RemoveQuotaRequestMultiError is an error wrapping multiple validation errors
// returned by RemoveQuotaRequest.ValidateAll() if the designated constraints
// aren't met.
type RemoveQuotaRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RemoveQuotaRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RemoveQuotaRequestMultiError) AllErrors() []error { return m }

// RemoveQuotaRequestValidationError is the validation error returned by
// RemoveQuotaRequest.Validate if the designated constraints aren't met.
type RemoveQuotaRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RemoveQuotaRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RemoveQuotaRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RemoveQuotaRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RemoveQuotaRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RemoveQuotaRequestValidationError) ErrorName() string {
	return "RemoveQuotaRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RemoveQuotaRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRemoveQuotaRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RemoveQuotaRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RemoveQuotaRequestValidationError{}

var _RemoveQuotaRequest_Path_Pattern = regexp.MustCompile("^/.*$")

// Validate checks the field values on MvRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MvRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MvRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MvRequestMultiError, or nil
// if none found.
func (m *MvRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *MvRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetVolume()) < 1 {
		err := MvRequestValidationError{
			field:  "Volume",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_MvRequest_Src_Pattern.MatchString(m.GetSrc()) {
		err := MvRequestValidationError{
			field:  "Src",
			reason: "value does not match regex pattern \"^/.*$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_MvRequest_Dst_Pattern.MatchString(m.GetDst()) {
		err := MvRequestValidationError{
			field:  "Dst",
			reason: "value does not match regex pattern \"^/.*$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return MvRequestMultiError(errors)
	}

	return nil
}

// MvRequestMultiError is an error wrapping multiple validation errors returned
// by MvRequest.ValidateAll() if the designated constraints aren't met.
type MvRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MvRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MvRequestMultiError) AllErrors() []error { return m }

// MvRequestValidationError is the validation error returned by
// MvRequest.Validate if the designated constraints aren't met.
type MvRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MvRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MvRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MvRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MvRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MvRequestValidationError) ErrorName() string { return "MvRequestValidationError" }

// Error satisfies the builtin error interface
func (e MvRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMvRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MvRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MvRequestValidationError{}

var _MvRequest_Src_Pattern = regexp.MustCompile("^/.*$")

var _MvRequest_Dst_Pattern = regexp.MustCompile("^/.*$")

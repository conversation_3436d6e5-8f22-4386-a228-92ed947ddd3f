# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: MetaDataMiddleware API
    version: 0.0.1
paths:
    /v1/posix/mv:
        post:
            tags:
                - MetaDataMiddleware
            operationId: MetaDataMiddleware_Mv
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/MvRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/quotas:
        get:
            tags:
                - MetaDataMiddleware
            description: |-
                list quotas for the given paths, batch operation
                 list all quotas when the path list param is nil,
                 return only the usage if the path has no quota set, and the return size = -1
            operationId: MetaDataMiddleware_ListQuota
            parameters:
                - name: volume
                  in: query
                  schema:
                    type: string
                - name: type
                  in: query
                  schema:
                    type: integer
                    format: enum
                - name: paths
                  in: query
                  schema:
                    type: array
                    items:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListQuotaReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        put:
            tags:
                - MetaDataMiddleware
            description: set or update the quota only, dir exists already, idempotence
            operationId: MetaDataMiddleware_SetQuota
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SetQuotaRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        post:
            tags:
                - MetaDataMiddleware
            description: create dir and set the quota, idempotence
            operationId: MetaDataMiddleware_CreateQuotaDir
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SetQuotaRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        delete:
            tags:
                - MetaDataMiddleware
            description: delete quota and the directory
            operationId: MetaDataMiddleware_RemoveQuotaDir
            parameters:
                - name: volume
                  in: query
                  schema:
                    type: string
                - name: path
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        patch:
            tags:
                - MetaDataMiddleware
            description: delete quota, dir remains
            operationId: MetaDataMiddleware_UnsetQuota
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UnsetQuotaRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
components:
    schemas:
        GoogleProtobufAny:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        ListQuotaReply:
            type: object
            properties:
                quotas:
                    type: array
                    items:
                        $ref: '#/components/schemas/Quota'
        MvRequest:
            type: object
            properties:
                volume:
                    type: string
                src:
                    type: string
                dst:
                    type: string
        Quota:
            type: object
            properties:
                volume:
                    type: string
                type:
                    type: integer
                    format: enum
                path:
                    type: string
                size:
                    type: string
                percent:
                    type: integer
                    format: int32
                used:
                    type: string
        SetQuotaRequest:
            type: object
            properties:
                volume:
                    type: string
                type:
                    type: integer
                    format: enum
                path:
                    type: string
                size:
                    type: string
                percent:
                    type: integer
                    format: int32
        Status:
            type: object
            properties:
                code:
                    type: integer
                    description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
                    format: int32
                message:
                    type: string
                    description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoogleProtobufAny'
                    description: A list of messages that carry the error details.  There is a common set of message types for APIs to use.
            description: 'The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).'
        UnsetQuotaRequest:
            type: object
            properties:
                volume:
                    type: string
                type:
                    type: integer
                    format: enum
                path:
                    type: string
tags:
    - name: MetaDataMiddleware

//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"

	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/biz"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/conf"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/data"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/server"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/service"
)

// wireApp init kratos application.
func wireApp(*conf.Server, *conf.Data, log.Logger) (*kratos.App, func(), error) {
	panic(wire.Build(
		server.ProviderSet, data.ProviderSet, biz.ProviderSet,
		service.ProviderSet, newApp))
}

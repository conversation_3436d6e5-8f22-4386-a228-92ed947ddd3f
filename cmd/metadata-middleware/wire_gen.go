// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/biz"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/conf"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/data"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/server"
	"git-plat.tecorigin.net/ai-platform/metadata-middleware/internal/service"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(confServer *conf.Server, confData *conf.Data, logger log.Logger) (*kratos.App, func(), error) {
	iDfsRepo, cleanup, err := data.NewDfsRepo(confData, logger)
	if err != nil {
		return nil, nil, err
	}
	quotaUsecase := biz.NewQuotaUsecase(iDfsRepo, logger)
	metaDataMiddlewareService := service.NewMetaDataMiddlewareService(quotaUsecase)
	grpcServer := server.NewGRPCServer(confServer, metaDataMiddlewareService, logger)
	httpServer := server.NewHTTPServer(confServer, metaDataMiddlewareService, logger)
	app := newApp(logger, grpcServer, httpServer)
	return app, func() {
		cleanup()
	}, nil
}
